# UTM SE Threaded Interpreter Module

这是从 UTM 项目中完整迁移的 UTM SE (Slow Edition) 线程化解释器实现。该模块提供了一个独立的、高性能的线程化解释器，可以在不支持 JIT 编译的环境中使用。

## 概述

UTM SE 使用了一种称为 TCTI (Tiny-Code Threaded Interpreter) 的先进解释器技术，而不是传统的 TCI (Tiny Code Interpreter)。TCTI 通过使用 gadget 链接技术实现了比传统解释器更好的性能，同时避免了 JIT 编译的要求。

### 主要特性

- **高性能线程化解释器**: 使用 gadget 链接技术，性能优于传统解释器
- **无 JIT 要求**: 适用于不允许动态代码生成的环境（如 iOS）
- **AArch64 专用**: 专门为 AArch64 架构优化
- **完整的 TCG 支持**: 支持完整的 QEMU TCG 操作集
- **约 40,960 个 gadgets**: 支持三操作数指令的大量 gadget 组合

## 目录结构

```
ThreadedInterpreter/
├── README.md                    # 本文档
├── Core/                        # 核心实现
│   ├── aarch64-tcti/           # AArch64 TCTI 实现
│   │   ├── tcg-target.c.inc    # 主要实现文件 (2212 行)
│   │   ├── tcg-target.h        # 主头文件
│   │   └── ...                 # 其他头文件
│   ├── tcg/                    # TCG 基础设施
│   │   ├── tcg.c              # TCG 核心实现
│   │   ├── optimize.c         # TCG 优化
│   │   └── ...                # 其他 TCG 文件
│   ├── include/               # 公共头文件
│   │   ├── threaded_interpreter.h  # 主 API 头文件
│   │   └── tcg/               # TCG 头文件
│   └── threaded_interpreter.c  # API 实现
├── Gadgets/                    # Gadget 相关
│   ├── Generator/             # Gadget 生成器
│   │   └── tcti-gadget-gen.py # Python 生成脚本 (1138 行)
│   └── Generated/             # 生成的 gadget 文件
├── Build/                     # 构建系统
│   ├── Makefile              # 主 Makefile
│   └── config.h              # 配置头文件
├── Tests/                     # 测试用例
│   ├── test_basic.c          # 基础测试
│   └── Makefile              # 测试 Makefile
└── Documentation/             # 详细文档
    └── ARCHITECTURE.md        # 架构文档
```

## 快速开始

### 系统要求

- **平台**: macOS 或 Linux
- **架构**: AArch64 (ARM64) - 这是唯一支持的架构
- **编译器**: Clang 或 GCC
- **Python**: Python 3.x (用于 gadget 生成)

### 编译

1. 进入构建目录：
```bash
cd TIDemo/TIDemo/ThreadedInterpreter/Build
```

2. 编译库：
```bash
make all
```

3. 这将：
   - 生成所有必要的 gadgets
   - 编译所有源文件
   - 创建 `libthreadedinterpreter.a` 静态库

### 运行测试

1. 进入测试目录：
```bash
cd ../Tests
```

2. 编译并运行测试：
```bash
make test
```

3. 检查平台支持：
```bash
make check-platform
```

## API 使用

### 基本使用示例

```c
#include "ThreadedInterpreter/Core/include/threaded_interpreter.h"

int main() {
    // 检查平台支持
    if (!threaded_interpreter_available()) {
        printf("平台不支持线程化解释器\n");
        return -1;
    }

    // 初始化
    if (threaded_interpreter_init() != 0) {
        printf("初始化失败\n");
        return -1;
    }

    // 生成 gadgets（如果需要）
    threaded_interpreter_generate_gadgets();

    // 执行翻译块
    // CPUArchState *env = ...; // 你的 CPU 状态
    // void *tb_ptr = ...;      // 翻译块指针
    // uintptr_t result = threaded_interpreter_exec(env, tb_ptr);

    // 清理
    threaded_interpreter_cleanup();

    return 0;
}
```

### 主要 API 函数

- `threaded_interpreter_available()`: 检查平台支持
- `threaded_interpreter_init()`: 初始化解释器
- `threaded_interpreter_cleanup()`: 清理资源
- `threaded_interpreter_exec()`: 执行翻译块
- `threaded_interpreter_generate_gadgets()`: 生成 gadgets

## 技术细节

### TCTI 架构

TCTI 使用了一种基于 gadget 的执行模型：

1. **Gadgets**: 小的汇编代码片段，每个对应一个 TCG 操作
2. **Thread Stream**: 包含 gadget 地址和操作数的指令流
3. **寄存器约定**:
   - x1-x15: 客户寄存器
   - x28: 线程流指针
   - x29: 帧指针
   - x30: 链接寄存器

### 性能特性

- **零开销分发**: 使用间接跳转链接 gadgets
- **寄存器缓存**: 客户寄存器映射到主机寄存器
- **优化的内存访问**: 专门的 load/store gadgets
- **SIMD 支持**: 完整的向量操作支持

## 构建选项

### Makefile 目标

- `make all`: 构建库（默认）
- `make gadgets`: 仅生成 gadgets
- `make clean`: 清理构建文件
- `make distclean`: 完全清理
- `make install`: 安装库
- `make help`: 显示帮助

### 配置选项

在 `Build/config.h` 中可以配置：

- `CONFIG_TCG_THREADED_INTERPRETER`: 启用线程化解释器
- `TCG_TARGET_REG_BITS`: 目标寄存器位数 (64)
- `TCG_TARGET_NB_REGS`: 寄存器数量 (64)
- `DEBUG`: 启用调试模式

## 故障排除

### 常见问题

1. **编译错误**: 确保在 AArch64 平台上编译
2. **Gadget 生成失败**: 检查 Python 3 是否可用
3. **链接错误**: 确保所有依赖文件都已正确复制

### 调试

启用调试模式：
```bash
make CFLAGS="-DDEBUG -g -O0"
```

## 许可证

本模块基于 UTM 项目，遵循相同的开源许可证。

## 贡献

这是一个从 UTM 项目迁移的完整实现。如需贡献或报告问题，请参考原始 UTM 项目。

## 更多信息

- 查看 `Documentation/ARCHITECTURE.md` 了解详细的架构信息
- 查看 `Core/aarch64-tcti/README.md` 了解 TCTI 实现细节
- 参考 UTM 项目的原始文档
