# Tiny-Code Threaded Interpreter (TCTI)

This directory contains the implementation of QEMU's "Tiny-Code Threaded Interpreter" (TCTI), which is a high-performance interpreter that uses a technique called "gadget chaining" to reduce the overhead of interpretation.

## Overview

The TCTI is designed to provide better performance than the traditional TCI (Tiny Code Interpreter) while still avoiding the need for JIT compilation. This makes it particularly useful on platforms where JIT compilation is not allowed or practical, such as iOS.

## Architecture

The TCTI works by pre-compiling small code fragments called "gadgets" for each TCG operation. These gadgets are then chained together at runtime to form the execution path. This approach reduces the interpreter overhead compared to traditional bytecode interpretation.

### Key Components

1. **Gadgets**: Small, pre-compiled code fragments that implement individual TCG operations
2. **Gadget Chaining**: The mechanism for linking gadgets together at runtime
3. **Register Management**: Efficient mapping between guest and host registers
4. **Memory Operations**: Optimized load/store operations with different alignment and endianness support

### Register Conventions

The TCTI uses the following register conventions on AArch64:

- **x1-x15**: Guest general-purpose registers
- **x28**: Thread-stream pointer (points to the current instruction stream)
- **x0**: Temporary register for operations
- **x16-x27**: Host-specific registers and temporaries
- **x29**: Frame pointer
- **x30**: Link register

### Gadget Types

The implementation includes gadgets for:

- **Arithmetic Operations**: add, sub, mul, div, etc.
- **Logical Operations**: and, or, xor, not, etc.
- **Bitwise Operations**: shifts, rotates, bit manipulation
- **Memory Operations**: loads and stores with various alignments
- **Control Flow**: branches, calls, returns
- **Vector Operations**: SIMD instructions
- **Extension Operations**: sign/zero extensions
- **Byte Swapping**: endianness conversion

## Performance Characteristics

The TCTI provides significant performance improvements over traditional TCI:

- Reduced interpreter overhead through gadget chaining
- Better register allocation and usage
- Optimized memory access patterns
- Efficient handling of common operation sequences

## Implementation Details

The implementation is split across several files:

- `tcg-target.h`: Main header with register definitions and constants
- `tcg-target.c.inc`: Core implementation with gadget execution logic
- `tcg-target-*.h`: Various configuration and constraint files
- `tcti-gadget-gen.py`: Python script that generates the gadget code

## Building

The TCTI is enabled with the `--enable-tcg-threaded-interpreter` configure option and requires AArch64 host architecture.

## Limitations

- Currently only supports AArch64 host architecture
- Gadgets are limited to three operands maximum
- Some complex operations (movcond, deposit, extract) are not yet supported as gadgets
- Experimental and may have stability issues

## Future Work

- Support for additional host architectures
- More complex gadget types
- Further performance optimizations
- Better debugging and profiling support
