/*
 * Main header for UTM SE Threaded Interpreter Module
 * 
 * This file provides the main interface for using the threaded interpreter
 * in standalone applications outside of the full QEMU/UTM environment.
 */

#ifndef THREADED_INTERPRETER_H
#define THREADED_INTERPRETER_H

/* Include our configuration */
#include "../Build/config.h"

/* Basic types and definitions */
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <inttypes.h>

/* Forward declarations */
typedef struct TCGContext TCGContext;
typedef struct TranslationBlock TranslationBlock;
typedef struct CPUArchState CPUArchState;
typedef struct CPUState CPUState;

/* TCG opcode definitions */
typedef enum TCGOpcode {
    INDEX_op_discard,
    INDEX_op_set_label,
    INDEX_op_call,
    INDEX_op_br,
    INDEX_op_mb,
    INDEX_op_insn_start,
    INDEX_op_exit_tb,
    INDEX_op_goto_tb,
    INDEX_op_goto_ptr,
    INDEX_op_qemu_ld_a32_i32,
    INDEX_op_qemu_ld_a64_i32,
    INDEX_op_qemu_st_a32_i32,
    INDEX_op_qemu_st_a64_i32,
    INDEX_op_qemu_ld_a32_i64,
    INDEX_op_qemu_ld_a64_i64,
    INDEX_op_qemu_st_a32_i64,
    INDEX_op_qemu_st_a64_i64,
    INDEX_op_qemu_ld_a32_i128,
    INDEX_op_qemu_ld_a64_i128,
    INDEX_op_qemu_st_a32_i128,
    INDEX_op_qemu_st_a64_i128,
    INDEX_op_mov_i32,
    INDEX_op_movi_i32,
    INDEX_op_setcond_i32,
    INDEX_op_negsetcond_i32,
    INDEX_op_movcond_i32,
    INDEX_op_ld8u_i32,
    INDEX_op_ld8s_i32,
    INDEX_op_ld16u_i32,
    INDEX_op_ld16s_i32,
    INDEX_op_ld_i32,
    INDEX_op_st8_i32,
    INDEX_op_st16_i32,
    INDEX_op_st_i32,
    INDEX_op_add_i32,
    INDEX_op_sub_i32,
    INDEX_op_mul_i32,
    INDEX_op_div_i32,
    INDEX_op_divu_i32,
    INDEX_op_rem_i32,
    INDEX_op_remu_i32,
    INDEX_op_div2_i32,
    INDEX_op_divu2_i32,
    INDEX_op_and_i32,
    INDEX_op_or_i32,
    INDEX_op_xor_i32,
    INDEX_op_shl_i32,
    INDEX_op_shr_i32,
    INDEX_op_sar_i32,
    INDEX_op_rotl_i32,
    INDEX_op_rotr_i32,
    INDEX_op_deposit_i32,
    INDEX_op_extract_i32,
    INDEX_op_sextract_i32,
    INDEX_op_extract2_i32,
    INDEX_op_add2_i32,
    INDEX_op_sub2_i32,
    INDEX_op_mulu2_i32,
    INDEX_op_muls2_i32,
    INDEX_op_muluh_i32,
    INDEX_op_mulsh_i32,
    INDEX_op_ext8s_i32,
    INDEX_op_ext16s_i32,
    INDEX_op_ext8u_i32,
    INDEX_op_ext16u_i32,
    INDEX_op_bswap16_i32,
    INDEX_op_bswap32_i32,
    INDEX_op_not_i32,
    INDEX_op_neg_i32,
    INDEX_op_andc_i32,
    INDEX_op_orc_i32,
    INDEX_op_eqv_i32,
    INDEX_op_nand_i32,
    INDEX_op_nor_i32,
    INDEX_op_clz_i32,
    INDEX_op_ctz_i32,
    INDEX_op_ctpop_i32,
    INDEX_op_mov_i64,
    INDEX_op_movi_i64,
    INDEX_op_setcond_i64,
    INDEX_op_negsetcond_i64,
    INDEX_op_movcond_i64,
    INDEX_op_ld8u_i64,
    INDEX_op_ld8s_i64,
    INDEX_op_ld16u_i64,
    INDEX_op_ld16s_i64,
    INDEX_op_ld32u_i64,
    INDEX_op_ld32s_i64,
    INDEX_op_ld_i64,
    INDEX_op_st8_i64,
    INDEX_op_st16_i64,
    INDEX_op_st32_i64,
    INDEX_op_st_i64,
    INDEX_op_add_i64,
    INDEX_op_sub_i64,
    INDEX_op_mul_i64,
    INDEX_op_div_i64,
    INDEX_op_divu_i64,
    INDEX_op_rem_i64,
    INDEX_op_remu_i64,
    INDEX_op_div2_i64,
    INDEX_op_divu2_i64,
    INDEX_op_and_i64,
    INDEX_op_or_i64,
    INDEX_op_xor_i64,
    INDEX_op_shl_i64,
    INDEX_op_shr_i64,
    INDEX_op_sar_i64,
    INDEX_op_rotl_i64,
    INDEX_op_rotr_i64,
    INDEX_op_deposit_i64,
    INDEX_op_extract_i64,
    INDEX_op_sextract_i64,
    INDEX_op_extract2_i64,
    INDEX_op_add2_i64,
    INDEX_op_sub2_i64,
    INDEX_op_mulu2_i64,
    INDEX_op_muls2_i64,
    INDEX_op_muluh_i64,
    INDEX_op_mulsh_i64,
    INDEX_op_ext8s_i64,
    INDEX_op_ext16s_i64,
    INDEX_op_ext32s_i64,
    INDEX_op_ext8u_i64,
    INDEX_op_ext16u_i64,
    INDEX_op_ext32u_i64,
    INDEX_op_bswap16_i64,
    INDEX_op_bswap32_i64,
    INDEX_op_bswap64_i64,
    INDEX_op_not_i64,
    INDEX_op_neg_i64,
    INDEX_op_andc_i64,
    INDEX_op_orc_i64,
    INDEX_op_eqv_i64,
    INDEX_op_nand_i64,
    INDEX_op_nor_i64,
    INDEX_op_clz_i64,
    INDEX_op_ctz_i64,
    INDEX_op_ctpop_i64,
    INDEX_op_extrl_i64_i32,
    INDEX_op_extrh_i64_i32,
    INDEX_op_ext_i32_i64,
    INDEX_op_extu_i32_i64,
    INDEX_op_brcond_i32,
    INDEX_op_brcond_i64,
    INDEX_op_setcond2_i32,
    INDEX_op_mulu2_i64,
    INDEX_op_brcond2_i32,
    INDEX_op_qemu_st8_i32,
    INDEX_op_last_generic,
    NB_OPS,
} TCGOpcode;

/* Main API functions */

/**
 * Initialize the threaded interpreter
 * @return 0 on success, negative on error
 */
int threaded_interpreter_init(void);

/**
 * Cleanup the threaded interpreter
 */
void threaded_interpreter_cleanup(void);

/**
 * Execute a translation block using the threaded interpreter
 * @param env CPU environment state
 * @param tb_ptr Pointer to the translation block
 * @return Exit code from execution
 */
uintptr_t threaded_interpreter_exec(CPUArchState *env, const void *tb_ptr);

/**
 * Generate gadgets for the threaded interpreter
 * @return 0 on success, negative on error
 */
int threaded_interpreter_generate_gadgets(void);

/**
 * Check if the threaded interpreter is available on this platform
 * @return true if available, false otherwise
 */
bool threaded_interpreter_available(void);

#endif /* THREADED_INTERPRETER_H */
