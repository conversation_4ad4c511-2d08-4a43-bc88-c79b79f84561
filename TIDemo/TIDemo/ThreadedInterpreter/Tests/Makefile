# Makefile for Threaded Interpreter Tests
#
# This Make<PERSON><PERSON> builds and runs tests for the threaded interpreter module.

# Compiler and flags
CC = clang
CFLAGS = -Wall -Wextra -O2 -g -std=gnu11
CPPFLAGS = -DCONFIG_TCG_THREADED_INTERPRETER=1 -DTCG_TARGET_INTERPRETER=1

# Architecture detection
ARCH := $(shell uname -m)
ifeq ($(ARCH),arm64)
    TCG_ARCH = aarch64-tcti
    CPPFLAGS += -DHOST_AARCH64=1 -DTCG_TARGET_REG_BITS=64 -DTCG_TARGET_NB_REGS=64
else
    $(warning Warning: Running on unsupported architecture $(ARCH). Tests may fail.)
    TCG_ARCH = aarch64-tcti
endif

# Directories
CORE_DIR = ../Core
BUILD_DIR = ../Build
INCLUDE_DIR = $(CORE_DIR)/include

# Include paths
INCLUDES = -I$(INCLUDE_DIR) -I$(BUILD_DIR) -I$(CORE_DIR)

# Source files
TEST_SOURCES = test_basic.c
CORE_SOURCES = $(CORE_DIR)/threaded_interpreter.c

# Object files
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)
CORE_OBJECTS = $(CORE_SOURCES:.c=.o)

# Test executables
TEST_BASIC = test_basic

# Default target
all: $(TEST_BASIC)

# Build basic test
$(TEST_BASIC): test_basic.o $(CORE_OBJECTS)
	@echo "Building test: $@"
	$(CC) $(CFLAGS) -o $@ $^

# Compile test files
%.o: %.c
	@echo "Compiling test: $<"
	$(CC) $(CFLAGS) $(CPPFLAGS) $(INCLUDES) -c $< -o $@

# Compile core files
$(CORE_DIR)/%.o: $(CORE_DIR)/%.c
	@echo "Compiling core: $<"
	$(CC) $(CFLAGS) $(CPPFLAGS) $(INCLUDES) -c $< -o $@

# Run tests
test: $(TEST_BASIC)
	@echo "=== Running Basic Tests ==="
	./$(TEST_BASIC)

# Run tests with verbose output
test-verbose: $(TEST_BASIC)
	@echo "=== Running Basic Tests (Verbose) ==="
	./$(TEST_BASIC) -v

# Check if we're on the right platform
check-platform:
	@echo "Current architecture: $(ARCH)"
	@echo "Target architecture: $(TCG_ARCH)"
	@if [ "$(ARCH)" = "arm64" ]; then \
		echo "✓ Platform supported"; \
	else \
		echo "⚠ Platform not officially supported (tests may fail)"; \
	fi

# Clean build artifacts
clean:
	rm -f $(TEST_OBJECTS) $(CORE_OBJECTS) $(TEST_BASIC)

# Clean everything
distclean: clean
	rm -f *.log *.tmp

# Help
help:
	@echo "Available targets:"
	@echo "  all           - Build all tests (default)"
	@echo "  test          - Run basic tests"
	@echo "  test-verbose  - Run tests with verbose output"
	@echo "  check-platform - Check if current platform is supported"
	@echo "  clean         - Remove build artifacts"
	@echo "  distclean     - Remove all generated files"
	@echo "  help          - Show this help message"

.PHONY: all test test-verbose check-platform clean distclean help
