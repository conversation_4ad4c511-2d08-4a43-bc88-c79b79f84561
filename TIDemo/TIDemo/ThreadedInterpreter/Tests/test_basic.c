/*
 * Basic tests for the threaded interpreter module
 * 
 * This file contains basic functionality tests to verify that the
 * threaded interpreter module is working correctly.
 */

#include "../Core/include/threaded_interpreter.h"
#include <stdio.h>
#include <stdlib.h>
#include <assert.h>

/* Test result tracking */
static int tests_run = 0;
static int tests_passed = 0;
static int tests_failed = 0;

#define TEST_ASSERT(condition, message) \
    do { \
        tests_run++; \
        if (condition) { \
            tests_passed++; \
            printf("✓ PASS: %s\n", message); \
        } else { \
            tests_failed++; \
            printf("✗ FAIL: %s\n", message); \
        } \
    } while(0)

/* Test platform availability */
void test_platform_availability(void)
{
    printf("\n=== Testing Platform Availability ===\n");
    
    bool available = threaded_interpreter_available();
    
#ifdef __aarch64__
    TEST_ASSERT(available == true, "Platform should be supported on AArch64");
#else
    TEST_ASSERT(available == false, "Platform should not be supported on non-AArch64");
#endif
    
    printf("Platform availability: %s\n", available ? "SUPPORTED" : "NOT SUPPORTED");
}

/* Test initialization and cleanup */
void test_init_cleanup(void)
{
    printf("\n=== Testing Initialization and Cleanup ===\n");
    
    /* Test initialization */
    int init_result = threaded_interpreter_init();
    TEST_ASSERT(init_result == 0, "Initialization should succeed");
    
    /* Test double initialization (should be safe) */
    int init_result2 = threaded_interpreter_init();
    TEST_ASSERT(init_result2 == 0, "Double initialization should be safe");
    
    /* Test cleanup */
    threaded_interpreter_cleanup();
    printf("Cleanup completed\n");
    
    /* Test cleanup when not initialized (should be safe) */
    threaded_interpreter_cleanup();
    printf("Double cleanup completed (should be safe)\n");
}

/* Test gadget generation */
void test_gadget_generation(void)
{
    printf("\n=== Testing Gadget Generation ===\n");
    
    int result = threaded_interpreter_generate_gadgets();
    TEST_ASSERT(result == 0, "Gadget generation should succeed");
}

/* Test execution interface */
void test_execution_interface(void)
{
    printf("\n=== Testing Execution Interface ===\n");
    
    /* Initialize first */
    int init_result = threaded_interpreter_init();
    TEST_ASSERT(init_result == 0, "Initialization should succeed for execution test");
    
    if (init_result == 0) {
        /* Test execution with null parameters (should handle gracefully) */
        uintptr_t result = threaded_interpreter_exec(NULL, NULL);
        printf("Execution result: %lu\n", (unsigned long)result);
        
        /* Test execution with dummy parameters */
        void *dummy_tb = (void*)0x1000;
        result = threaded_interpreter_exec(NULL, dummy_tb);
        printf("Execution result with dummy TB: %lu\n", (unsigned long)result);
        
        TEST_ASSERT(true, "Execution interface should handle calls gracefully");
    }
    
    threaded_interpreter_cleanup();
}

/* Test configuration and constants */
void test_configuration(void)
{
    printf("\n=== Testing Configuration ===\n");
    
    /* Test that important constants are defined */
    #ifdef CONFIG_TCG_THREADED_INTERPRETER
    TEST_ASSERT(true, "CONFIG_TCG_THREADED_INTERPRETER should be defined");
    #else
    TEST_ASSERT(false, "CONFIG_TCG_THREADED_INTERPRETER should be defined");
    #endif
    
    #ifdef TCG_TARGET_INTERPRETER
    TEST_ASSERT(true, "TCG_TARGET_INTERPRETER should be defined");
    #else
    TEST_ASSERT(false, "TCG_TARGET_INTERPRETER should be defined");
    #endif
    
    #ifdef TCG_TARGET_REG_BITS
    TEST_ASSERT(TCG_TARGET_REG_BITS == 64, "TCG_TARGET_REG_BITS should be 64");
    printf("TCG_TARGET_REG_BITS = %d\n", TCG_TARGET_REG_BITS);
    #else
    TEST_ASSERT(false, "TCG_TARGET_REG_BITS should be defined");
    #endif
    
    #ifdef TCG_TARGET_NB_REGS
    TEST_ASSERT(TCG_TARGET_NB_REGS == 64, "TCG_TARGET_NB_REGS should be 64");
    printf("TCG_TARGET_NB_REGS = %d\n", TCG_TARGET_NB_REGS);
    #else
    TEST_ASSERT(false, "TCG_TARGET_NB_REGS should be defined");
    #endif
}

/* Test file structure */
void test_file_structure(void)
{
    printf("\n=== Testing File Structure ===\n");
    
    /* Test that key files exist by trying to open them */
    FILE *f;
    
    /* Test gadget generator */
    f = fopen("../Gadgets/Generator/tcti-gadget-gen.py", "r");
    if (f) {
        fclose(f);
        TEST_ASSERT(true, "Gadget generator script exists");
    } else {
        TEST_ASSERT(false, "Gadget generator script should exist");
    }
    
    /* Test core implementation */
    f = fopen("../Core/aarch64-tcti/tcg-target.c.inc", "r");
    if (f) {
        fclose(f);
        TEST_ASSERT(true, "Core TCTI implementation exists");
    } else {
        TEST_ASSERT(false, "Core TCTI implementation should exist");
    }
    
    /* Test main header */
    f = fopen("../Core/aarch64-tcti/tcg-target.h", "r");
    if (f) {
        fclose(f);
        TEST_ASSERT(true, "Main TCTI header exists");
    } else {
        TEST_ASSERT(false, "Main TCTI header should exist");
    }
}

/* Main test runner */
int main(int argc, char *argv[])
{
    printf("=== UTM SE Threaded Interpreter Test Suite ===\n");
    printf("Running comprehensive tests...\n");
    
    /* Run all tests */
    test_platform_availability();
    test_configuration();
    test_file_structure();
    test_init_cleanup();
    test_gadget_generation();
    test_execution_interface();
    
    /* Print summary */
    printf("\n=== Test Summary ===\n");
    printf("Tests run: %d\n", tests_run);
    printf("Tests passed: %d\n", tests_passed);
    printf("Tests failed: %d\n", tests_failed);
    
    if (tests_failed == 0) {
        printf("🎉 All tests passed!\n");
        return 0;
    } else {
        printf("❌ %d test(s) failed\n", tests_failed);
        return 1;
    }
}
