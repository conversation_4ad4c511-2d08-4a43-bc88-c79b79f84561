# Makefile for UTM SE Threaded Interpreter Module
# 
# This <PERSON><PERSON><PERSON> builds the complete threaded interpreter implementation
# extracted from the UTM project.

# Compiler and flags
CC = clang
CFLAGS = -Wall -Wextra -O2 -g -std=gnu11
CPPFLAGS = -DCONFIG_TCG_THREADED_INTERPRETER=1 -DTCG_TARGET_INTERPRETER=1

# Architecture detection
ARCH := $(shell uname -m)
ifeq ($(ARCH),arm64)
    TCG_ARCH = aarch64-tcti
    CPPFLAGS += -DHOST_AARCH64=1
else
    $(error Unsupported architecture: $(ARCH). Only arm64/aarch64 is supported.)
endif

# Directories
SRCDIR = ..
CORE_DIR = $(SRCDIR)/Core
TCG_DIR = $(CORE_DIR)/tcg
TCTI_DIR = $(CORE_DIR)/$(TCG_ARCH)
GADGET_DIR = $(SRCDIR)/Gadgets
GENERATOR_DIR = $(GADGET_DIR)/Generator
GENERATED_DIR = $(GADGET_DIR)/Generated

# Include paths
INCLUDES = -I$(CORE_DIR) -I$(TCG_DIR) -I$(TCTI_DIR) -I$(GENERATED_DIR)

# Source files
TCG_SOURCES = \
    $(TCG_DIR)/tcg.c \
    $(TCG_DIR)/optimize.c \
    $(TCG_DIR)/region.c \
    $(TCG_DIR)/tcg-common.c \
    $(TCG_DIR)/tcg-op.c \
    $(TCG_DIR)/tcg-op-ldst.c \
    $(TCG_DIR)/tcg-op-gvec.c \
    $(TCG_DIR)/tcg-op-vec.c

TCTI_SOURCES = \
    $(TCTI_DIR)/tcg-target.c.inc

# Generated gadget sources (will be created by gadget generator)
GADGET_SOURCES = \
    $(GENERATED_DIR)/tcti_misc_gadgets.c \
    $(GENERATED_DIR)/tcti_setcond_gadgets.c \
    $(GENERATED_DIR)/tcti_brcond_gadgets.c \
    $(GENERATED_DIR)/tcti_mov_gadgets.c \
    $(GENERATED_DIR)/tcti_load_signed_gadgets.c \
    $(GENERATED_DIR)/tcti_load_unsigned_gadgets.c \
    $(GENERATED_DIR)/tcti_store_gadgets.c \
    $(GENERATED_DIR)/tcti_arithmetic_gadgets.c \
    $(GENERATED_DIR)/tcti_logical_gadgets.c \
    $(GENERATED_DIR)/tcti_extension_gadgets.c \
    $(GENERATED_DIR)/tcti_bitwise_gadgets.c \
    $(GENERATED_DIR)/tcti_byteswap_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_ld_aligned_signed_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_ld_unaligned_signed_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_ld_slowpath_signed_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_ld_aligned_unsigned_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_ld_unaligned_unsigned_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_ld_slowpath_unsigned_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_st_aligned_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_st_unaligned_le_gadgets.c \
    $(GENERATED_DIR)/tcti_qemu_st_slowpath_le_gadgets.c \
    $(GENERATED_DIR)/tcti_simd_base_gadgets.c \
    $(GENERATED_DIR)/tcti_simd_arithmetic_gadgets.c \
    $(GENERATED_DIR)/tcti_simd_logical_gadgets.c \
    $(GENERATED_DIR)/tcti_simd_immediate_gadgets.c

# All sources
ALL_SOURCES = $(TCG_SOURCES) $(TCTI_SOURCES) $(GADGET_SOURCES)

# Object files
OBJECTS = $(ALL_SOURCES:.c=.o)

# Target library
TARGET = libthreadedinterpreter.a

# Gadget generator
GADGET_GENERATOR = $(GENERATOR_DIR)/tcti-gadget-gen.py

# Default target
all: $(TARGET)

# Generate gadgets first
gadgets: $(GENERATED_DIR)/tcti_gadgets.h

$(GENERATED_DIR)/tcti_gadgets.h: $(GADGET_GENERATOR)
	@echo "Generating TCTI gadgets..."
	@mkdir -p $(GENERATED_DIR)
	cd $(GENERATED_DIR) && python3 $(GADGET_GENERATOR)

# Build the library
$(TARGET): gadgets $(OBJECTS)
	@echo "Creating library $(TARGET)..."
	ar rcs $@ $(OBJECTS)

# Compile source files
%.o: %.c
	@echo "Compiling $<..."
	$(CC) $(CFLAGS) $(CPPFLAGS) $(INCLUDES) -c $< -o $@

# Clean build artifacts
clean:
	rm -f $(OBJECTS) $(TARGET)
	rm -rf $(GENERATED_DIR)/*

# Clean everything including generated files
distclean: clean
	rm -rf $(GENERATED_DIR)

# Install (copy to a system location)
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	# Add installation commands here if needed

# Help
help:
	@echo "Available targets:"
	@echo "  all       - Build the threaded interpreter library (default)"
	@echo "  gadgets   - Generate TCTI gadgets"
	@echo "  clean     - Remove build artifacts"
	@echo "  distclean - Remove all generated files"
	@echo "  install   - Install the library"
	@echo "  help      - Show this help message"

.PHONY: all gadgets clean distclean install help
