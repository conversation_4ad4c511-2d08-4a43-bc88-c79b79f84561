/*
 * Configuration header for UTM SE Threaded Interpreter Module
 * 
 * This file contains the necessary configuration defines for building
 * the threaded interpreter outside of the full QEMU/UTM environment.
 */

#ifndef THREADED_INTERPRETER_CONFIG_H
#define THREADED_INTERPRETER_CONFIG_H

/* Basic configuration */
#define CONFIG_TCG 1
#define CONFIG_TCG_THREADED_INTERPRETER 1
#define TCG_TARGET_INTERPRETER 1

/* Host architecture */
#ifdef __aarch64__
#define HOST_AARCH64 1
#define TCG_TARGET_REG_BITS 64
#else
#error "Only AArch64 host architecture is supported"
#endif

/* Target architecture support */
#define TARGET_AARCH64 1
#define TARGET_LONG_BITS 64

/* Memory management */
#define CONFIG_USER_ONLY 1
#define TARGET_PAGE_BITS 12
#define TARGET_PAGE_SIZE (1 << TARGET_PAGE_BITS)
#define TARGET_PAGE_MASK ~(TARGET_PAGE_SIZE - 1)

/* TCG configuration */
#define TCG_TARGET_INSN_UNIT_SIZE 1
#define MAX_CODE_GEN_BUFFER_SIZE ((size_t)-1)
#define TCG_TARGET_NB_REGS 64
#define TCG_TARGET_GP_REGS 16

/* TCTI specific configuration */
#define TCTI_GADGET_IMMEDIATE_ARRAY_LEN 64
#define TCG_TARGET_CALL_STACK_OFFSET 0
#define TCG_TARGET_STACK_ALIGN 16

/* Calling conventions */
#define TCG_TARGET_CALL_ARG_I32 TCG_CALL_ARG_NORMAL
#define TCG_TARGET_CALL_ARG_I64 TCG_CALL_ARG_NORMAL
#define TCG_TARGET_CALL_ARG_I128 TCG_CALL_ARG_NORMAL
#define TCG_TARGET_CALL_RET_I128 TCG_CALL_RET_NORMAL

/* Feature flags */
#define HAVE_TCG_QEMU_TB_EXEC 1

/* Debug configuration */
#ifdef DEBUG
#define CONFIG_DEBUG_TCG 1
#endif

/* Disable features not needed for standalone operation */
#undef CONFIG_SOFTMMU
#undef CONFIG_PLUGINS
#undef CONFIG_PROFILER

/* Memory ordering */
#define TCG_TARGET_DEFAULT_MO 0

/* Vector support */
#define TCG_TARGET_HAS_v64 1
#define TCG_TARGET_HAS_v128 1
#define TCG_TARGET_HAS_v256 0

/* Standard library includes */
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <inttypes.h>

/* GLib compatibility (minimal subset) */
#ifndef g_assert_not_reached
#define g_assert_not_reached() assert(0)
#endif

#ifndef g_assert
#define g_assert(expr) assert(expr)
#endif

/* QEMU compatibility types */
typedef uint64_t target_ulong;
typedef int64_t target_long;
typedef uint32_t MemOp;

/* TCG types */
typedef enum TCGType {
    TCG_TYPE_I32,
    TCG_TYPE_I64,
    TCG_TYPE_V64,
    TCG_TYPE_V128,
    TCG_TYPE_V256,
    TCG_TYPE_COUNT
} TCGType;

typedef enum TCGCond {
    TCG_COND_NEVER  = 0 | 0 | 0 | 0,
    TCG_COND_ALWAYS = 0 | 0 | 0 | 1,
    TCG_COND_EQ     = 8 | 0 | 0 | 0,
    TCG_COND_NE     = 8 | 0 | 0 | 1,
    TCG_COND_LT     = 0 | 0 | 2 | 0,
    TCG_COND_GE     = 0 | 0 | 2 | 1,
    TCG_COND_LE     = 8 | 0 | 2 | 0,
    TCG_COND_GT     = 8 | 0 | 2 | 1,
    TCG_COND_LTU    = 0 | 4 | 0 | 0,
    TCG_COND_GEU    = 0 | 4 | 0 | 1,
    TCG_COND_LEU    = 8 | 4 | 0 | 0,
    TCG_COND_GTU    = 8 | 4 | 0 | 1,
} TCGCond;

/* Memory operation types */
typedef enum {
    MO_8     = 0,
    MO_16    = 1,
    MO_32    = 2,
    MO_64    = 3,
    MO_SIZE  = 3,
    
    MO_SIGN  = 4,
    MO_BSWAP = 8,
    
    MO_LE    = 0,
    MO_BE    = MO_BSWAP,
    
    MO_UB    = MO_8,
    MO_UW    = MO_16,
    MO_UL    = MO_32,
    MO_UQ    = MO_64,
    MO_SB    = MO_SIGN | MO_8,
    MO_SW    = MO_SIGN | MO_16,
    MO_SL    = MO_SIGN | MO_32,
    MO_SQ    = MO_SIGN | MO_64,
} MemOp;

/* Calling convention types */
typedef enum {
    TCG_CALL_ARG_NORMAL,
    TCG_CALL_ARG_BY_REF,
    TCG_CALL_ARG_BY_REF_N,
    TCG_CALL_RET_NORMAL,
    TCG_CALL_RET_BY_REF,
} TCGCallArgType, TCGCallReturnType;

#endif /* THREADED_INTERPRETER_CONFIG_H */
