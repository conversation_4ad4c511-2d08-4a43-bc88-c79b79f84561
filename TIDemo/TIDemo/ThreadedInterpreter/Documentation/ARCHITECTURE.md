# UTM SE Threaded Interpreter 架构文档

## 概述

UTM SE (Slow Edition) 使用了一种称为 TCTI (Tiny-Code Threaded Interpreter) 的先进解释器技术。这是一个专门为 AArch64 架构设计的高性能解释器，通过使用 gadget 链接技术避免了传统解释器的性能瓶颈。

## 核心概念

### 1. Threaded Interpreter vs Traditional Interpreter

**传统解释器 (TCI)**:
```
while (true) {
    opcode = fetch_opcode();
    switch (opcode) {
        case OP_ADD: /* 执行加法 */ break;
        case OP_SUB: /* 执行减法 */ break;
        // ...
    }
}
```

**线程化解释器 (TCTI)**:
```
// 每个操作都是一个 gadget，直接跳转到下一个
gadget_add:
    // 执行加法
    ldr x0, [x28], #8    // 加载下一个 gadget 地址
    br x0                // 直接跳转，无需 switch

gadget_sub:
    // 执行减法
    ldr x0, [x28], #8    // 加载下一个 gadget 地址
    br x0                // 直接跳转
```

### 2. Gadget 架构

每个 gadget 是一个小的汇编代码片段，对应一个 TCG 操作：

```assembly
tcti_gadget_add_i32:
    // 从 thread stream 读取操作数
    ldr w0, [x28], #4    // arg0 (destination register)
    ldr w1, [x28], #4    // arg1 (source register 1)
    ldr w2, [x28], #4    // arg2 (source register 2)
    
    // 执行操作
    add w1, w1, w2       // w1 = w1 + w2
    str w1, [x0]         // 存储结果到目标寄存器
    
    // 跳转到下一个 gadget
    ldr x0, [x28], #8    // 读取下一个 gadget 地址
    br x0                // 跳转
```

### 3. 寄存器约定

TCTI 使用固定的寄存器约定来优化性能：

- **x1-x15**: 客户寄存器 (guest registers)
- **x28**: 线程流指针 (thread stream pointer)
- **x29**: 帧指针 (frame pointer)
- **x30**: 链接寄存器 (link register)
- **x0**: 临时寄存器，用于地址计算和跳转

## 执行流程

### 1. 初始化阶段

```c
// 1. 生成所有 gadgets
tcti_generate_gadgets();

// 2. 初始化寄存器映射
tcti_init_register_mapping();

// 3. 设置线程流
tcti_setup_thread_stream();
```

### 2. 翻译阶段

TCG 前端将目标指令翻译为 TCG 操作序列，然后 TCTI 后端将其转换为 gadget 序列：

```
目标指令: add r1, r2, r3
    ↓
TCG 操作: tcg_gen_add_i32(r1, r2, r3)
    ↓
Thread Stream: [gadget_add_i32_addr, r1_offset, r2_offset, r3_offset, next_gadget_addr]
```

### 3. 执行阶段

```c
uintptr_t tcg_qemu_tb_exec(CPUArchState *env, const void *tb_ptr) {
    // 设置寄存器状态
    setup_guest_registers(env);
    
    // 设置线程流指针
    x28 = (uint64_t)tb_ptr;
    
    // 跳转到第一个 gadget
    void *first_gadget = *(void**)x28;
    x28 += 8;
    
    // 开始执行 gadget 链
    goto *first_gadget;
}
```

## Gadget 类型

### 1. 基础算术 Gadgets

- **tcti_add_i32/i64**: 32/64位加法
- **tcti_sub_i32/i64**: 32/64位减法
- **tcti_mul_i32/i64**: 32/64位乘法
- **tcti_div_i32/i64**: 32/64位除法

### 2. 逻辑运算 Gadgets

- **tcti_and_i32/i64**: 按位与
- **tcti_or_i32/i64**: 按位或
- **tcti_xor_i32/i64**: 按位异或
- **tcti_not_i32/i64**: 按位取反

### 3. 内存访问 Gadgets

- **tcti_ld_i32/i64**: 内存加载
- **tcti_st_i32/i64**: 内存存储
- **tcti_qemu_ld_***: QEMU 特定的加载操作
- **tcti_qemu_st_***: QEMU 特定的存储操作

### 4. 控制流 Gadgets

- **tcti_brcond_i32/i64**: 条件分支
- **tcti_setcond_i32/i64**: 条件设置
- **tcti_movcond_i32/i64**: 条件移动

### 5. SIMD/向量 Gadgets

- **tcti_simd_add_v128**: 128位向量加法
- **tcti_simd_mul_v128**: 128位向量乘法
- **tcti_simd_load_v128**: 128位向量加载

## 性能优化

### 1. 零开销分发

传统解释器需要 switch 语句来分发操作，TCTI 通过直接跳转消除了这个开销：

```
传统: fetch → decode → switch → execute → repeat
TCTI:  execute → jump → execute → jump → ...
```

### 2. 寄存器缓存

客户寄存器直接映射到主机寄存器，避免了频繁的内存访问：

```c
// 传统方式
int guest_r1 = env->regs[1];  // 内存访问
int guest_r2 = env->regs[2];  // 内存访问
env->regs[1] = guest_r1 + guest_r2;  // 内存访问

// TCTI 方式
// x1 直接对应 guest_r1，x2 直接对应 guest_r2
add x1, x1, x2  // 直接寄存器操作
```

### 3. 预取和流水线

Thread stream 的线性布局有利于 CPU 的预取机制：

```
Thread Stream: [gadget1_addr, args..., gadget2_addr, args..., gadget3_addr, args...]
                     ↑                      ↑                      ↑
                   当前执行              CPU预取                 CPU预取
```

## 内存布局

### 1. Thread Stream 结构

```
+------------------+
| Gadget 1 Address |  8 bytes
+------------------+
| Arg 1            |  4 bytes
+------------------+
| Arg 2            |  4 bytes
+------------------+
| Arg 3            |  4 bytes
+------------------+
| Gadget 2 Address |  8 bytes
+------------------+
| ...              |
+------------------+
```

### 2. 寄存器映射表

```c
typedef struct {
    uint64_t guest_regs[32];    // 客户寄存器
    uint64_t host_regs[32];     // 主机寄存器映射
    void *thread_stream;        // 当前线程流位置
    void *gadget_table[NB_OPS]; // Gadget 地址表
} TCTIContext;
```

## 错误处理

### 1. 异常处理

```c
// 每个 gadget 都包含异常检查
tcti_gadget_div_i32:
    // 检查除零
    cbz w2, division_by_zero
    
    // 正常执行
    udiv w1, w1, w2
    
    // 跳转到下一个 gadget
    ldr x0, [x28], #8
    br x0

division_by_zero:
    // 设置异常标志并退出
    mov x0, #EXCEPTION_DIVISION_BY_ZERO
    ret
```

### 2. 调试支持

```c
#ifdef DEBUG_TCTI
#define TCTI_DEBUG_TRACE(gadget_name) \
    do { \
        printf("Executing gadget: %s\n", #gadget_name); \
        printf("Thread stream: %p\n", (void*)x28); \
    } while(0)
#else
#define TCTI_DEBUG_TRACE(gadget_name)
#endif
```

## 与 QEMU TCG 的集成

### 1. TCG 后端接口

```c
// tcg-target.c.inc 中的主要函数
void tcg_target_init(TCGContext *s);
void tcg_target_qemu_prologue(TCGContext *s);
void tcg_out_op(TCGContext *s, TCGOpcode opc, const TCGArg *args);
```

### 2. 代码生成

```c
static void tcg_out_op(TCGContext *s, TCGOpcode opc, const TCGArg *args) {
    switch (opc) {
    case INDEX_op_add_i32:
        // 生成 thread stream 条目
        tcg_out_gadget_addr(s, tcti_gadget_add_i32);
        tcg_out_arg(s, args[0]);  // 目标寄存器
        tcg_out_arg(s, args[1]);  // 源寄存器1
        tcg_out_arg(s, args[2]);  // 源寄存器2
        break;
    // ...
    }
}
```

## 总结

TCTI 通过以下技术实现了高性能的解释执行：

1. **Gadget 链接**: 消除了传统解释器的分发开销
2. **寄存器映射**: 减少了内存访问
3. **线性内存布局**: 提高了缓存效率
4. **专门化操作**: 每个 gadget 都针对特定操作优化

这使得 TCTI 在不使用 JIT 编译的情况下，仍能获得接近 JIT 的性能表现。
