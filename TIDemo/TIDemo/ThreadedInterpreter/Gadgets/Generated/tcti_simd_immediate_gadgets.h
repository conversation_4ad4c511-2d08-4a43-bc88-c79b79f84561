/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern void* gadget_movi_cmode_e_op0_q0[16][256];
extern void* gadget_movi_cmode_e_op0_q1[16][256];
extern void* gadget_movi_cmode_e_op1_q0[16][256];
extern void* gadget_movi_cmode_e_op1_q1[16][256];
extern void* gadget_movi_cmode_8_op0_q0[16][256];
extern void* gadget_movi_cmode_8_op0_q1[16][256];
extern void* gadget_mvni_cmode_8_op0_q0[16][256];
extern void* gadget_mvni_cmode_8_op0_q1[16][256];
extern void* gadget_movi_cmode_a_op0_q0[16][256];
extern void* gadget_movi_cmode_a_op0_q1[16][256];
extern void* gadget_mvni_cmode_a_op0_q0[16][256];
extern void* gadget_mvni_cmode_a_op0_q1[16][256];
extern void* gadget_orr_cmode_a_op0_q0[16][256];
extern void* gadget_orr_cmode_a_op0_q1[16][256];
