/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_st8[16][16];
extern const void* gadget_st8_imm[16][16][64];
extern const void* gadget_st8_sh8_imm[16][16][64];
extern const void* gadget_st8_neg_imm[16][16][64];
extern const void* gadget_st16[16][16];
extern const void* gadget_st16_imm[16][16][64];
extern const void* gadget_st16_sh8_imm[16][16][64];
extern const void* gadget_st16_neg_imm[16][16][64];
extern const void* gadget_st_i32[16][16];
extern const void* gadget_st_i32_imm[16][16][64];
extern const void* gadget_st_i32_sh8_imm[16][16][64];
extern const void* gadget_st_i32_neg_imm[16][16][64];
extern const void* gadget_st_i64[16][16];
extern const void* gadget_st_i64_imm[16][16][64];
extern const void* gadget_st_i64_sh8_imm[16][16][64];
extern const void* gadget_st_i64_neg_imm[16][16][64];
