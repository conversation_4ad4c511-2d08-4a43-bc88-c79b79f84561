/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_qemu_ld_ub_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_ld_ub_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_ld_leuw_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_ld_leul_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_ld_leq_unaligned_off128_i64[16][16];
