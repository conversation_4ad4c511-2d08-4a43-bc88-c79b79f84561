/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_add_16b[16][16][16];
extern const void* gadget_add_8b[16][16][16];
extern const void* gadget_add_4h[16][16][16];
extern const void* gadget_add_8h[16][16][16];
extern const void* gadget_add_2s[16][16][16];
extern const void* gadget_add_4s[16][16][16];
extern const void* gadget_add_2d[16][16][16];
extern const void* gadget_add_scalar[16][16][16];
extern const void* gadget_usadd_16b[16][16][16];
extern const void* gadget_usadd_8b[16][16][16];
extern const void* gadget_usadd_4h[16][16][16];
extern const void* gadget_usadd_8h[16][16][16];
extern const void* gadget_usadd_2s[16][16][16];
extern const void* gadget_usadd_4s[16][16][16];
extern const void* gadget_usadd_2d[16][16][16];
extern const void* gadget_usadd_scalar[16][16][16];
extern const void* gadget_ssadd_16b[16][16][16];
extern const void* gadget_ssadd_8b[16][16][16];
extern const void* gadget_ssadd_4h[16][16][16];
extern const void* gadget_ssadd_8h[16][16][16];
extern const void* gadget_ssadd_2s[16][16][16];
extern const void* gadget_ssadd_4s[16][16][16];
extern const void* gadget_ssadd_2d[16][16][16];
extern const void* gadget_ssadd_scalar[16][16][16];
extern const void* gadget_sub_16b[16][16][16];
extern const void* gadget_sub_8b[16][16][16];
extern const void* gadget_sub_4h[16][16][16];
extern const void* gadget_sub_8h[16][16][16];
extern const void* gadget_sub_2s[16][16][16];
extern const void* gadget_sub_4s[16][16][16];
extern const void* gadget_sub_2d[16][16][16];
extern const void* gadget_sub_scalar[16][16][16];
extern const void* gadget_ussub_16b[16][16][16];
extern const void* gadget_ussub_8b[16][16][16];
extern const void* gadget_ussub_4h[16][16][16];
extern const void* gadget_ussub_8h[16][16][16];
extern const void* gadget_ussub_2s[16][16][16];
extern const void* gadget_ussub_4s[16][16][16];
extern const void* gadget_ussub_2d[16][16][16];
extern const void* gadget_ussub_scalar[16][16][16];
extern const void* gadget_sssub_16b[16][16][16];
extern const void* gadget_sssub_8b[16][16][16];
extern const void* gadget_sssub_4h[16][16][16];
extern const void* gadget_sssub_8h[16][16][16];
extern const void* gadget_sssub_2s[16][16][16];
extern const void* gadget_sssub_4s[16][16][16];
extern const void* gadget_sssub_2d[16][16][16];
extern const void* gadget_sssub_scalar[16][16][16];
extern const void* gadget_mul_16b[16][16][16];
extern const void* gadget_mul_8b[16][16][16];
extern const void* gadget_mul_4h[16][16][16];
extern const void* gadget_mul_8h[16][16][16];
extern const void* gadget_mul_2s[16][16][16];
extern const void* gadget_mul_4s[16][16][16];
extern const void* gadget_smax_16b[16][16][16];
extern const void* gadget_smax_8b[16][16][16];
extern const void* gadget_smax_4h[16][16][16];
extern const void* gadget_smax_8h[16][16][16];
extern const void* gadget_smax_2s[16][16][16];
extern const void* gadget_smax_4s[16][16][16];
extern const void* gadget_smin_16b[16][16][16];
extern const void* gadget_smin_8b[16][16][16];
extern const void* gadget_smin_4h[16][16][16];
extern const void* gadget_smin_8h[16][16][16];
extern const void* gadget_smin_2s[16][16][16];
extern const void* gadget_smin_4s[16][16][16];
extern const void* gadget_umax_16b[16][16][16];
extern const void* gadget_umax_8b[16][16][16];
extern const void* gadget_umax_4h[16][16][16];
extern const void* gadget_umax_8h[16][16][16];
extern const void* gadget_umax_2s[16][16][16];
extern const void* gadget_umax_4s[16][16][16];
extern const void* gadget_umin_16b[16][16][16];
extern const void* gadget_umin_8b[16][16][16];
extern const void* gadget_umin_4h[16][16][16];
extern const void* gadget_umin_8h[16][16][16];
extern const void* gadget_umin_2s[16][16][16];
extern const void* gadget_umin_4s[16][16][16];
