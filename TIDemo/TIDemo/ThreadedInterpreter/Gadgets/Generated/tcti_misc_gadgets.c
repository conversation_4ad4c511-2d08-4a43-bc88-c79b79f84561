/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#include "tcti_misc_gadgets.h"

__attribute__((naked)) void gadget_call(void)
{
	asm(
		"ldr x27, [x28], #8 \n"
		"str x28, [x25] \n"
		"stp x14, x15, [sp, #-16]! \n"
		"stp x28, lr,  [sp, #-16]! \n"
		"blr x27 \n"
		"mov x27, x0 \n"
		"ldp x28, lr,  [sp], #16 \n"
		"ldp x14, x15, [sp], #16 \n"
		"mov x0, x27 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

__attribute__((naked)) void gadget_br(void)
{
	asm(
		"ldr x28, [x28] \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

__attribute__((naked)) void gadget_exit_tb(void)
{
	asm(
		"ldr x0, [x28], #8 \n"
		"ret \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

__attribute__((naked)) void gadget_mb_all(void)
{
	asm(
		"dmb ish \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

__attribute__((naked)) void gadget_mb_st(void)
{
	asm(
		"dmb ishst \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

__attribute__((naked)) void gadget_mb_ld(void)
{
	asm(
		"dmb ishld \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

