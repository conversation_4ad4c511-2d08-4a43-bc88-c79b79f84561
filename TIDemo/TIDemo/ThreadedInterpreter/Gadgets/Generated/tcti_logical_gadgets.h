/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_not_i32[16][16];
extern const void* gadget_not_i64[16][16];
extern const void* gadget_neg_i32[16][16];
extern const void* gadget_neg_i64[16][16];
extern const void* gadget_and_i32[16][16][16];
extern const void* gadget_and_i64[16][16][16];
extern const void* gadget_andc_i32[16][16][16];
extern const void* gadget_andc_i64[16][16][16];
extern const void* gadget_or_i32[16][16][16];
extern const void* gadget_or_i64[16][16][16];
extern const void* gadget_orc_i32[16][16][16];
extern const void* gadget_orc_i64[16][16][16];
extern const void* gadget_xor_i32[16][16][16];
extern const void* gadget_xor_i64[16][16][16];
extern const void* gadget_eqv_i32[16][16][16];
extern const void* gadget_eqv_i64[16][16][16];
extern const void* gadget_shl_i32[16][16][16];
extern const void* gadget_shl_i64[16][16][16];
extern const void* gadget_shr_i32[16][16][16];
extern const void* gadget_shr_i64[16][16][16];
extern const void* gadget_sar_i32[16][16][16];
extern const void* gadget_sar_i64[16][16][16];
extern const void* gadget_rotr_i32[16][16][16];
extern const void* gadget_rotr_i64[16][16][16];
extern const void* gadget_rotl_i32[16][16][16];
extern const void* gadget_rotl_i64[16][16][16];
extern const void* gadget_nand_i32[16][16][16];
extern const void* gadget_nand_i64[16][16][16];
extern const void* gadget_nor_i32[16][16][16];
extern const void* gadget_nor_i64[16][16][16];
