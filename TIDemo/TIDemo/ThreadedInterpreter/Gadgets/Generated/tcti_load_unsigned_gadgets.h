/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_ld8u[16][16];
extern const void* gadget_ld8u_imm[16][16][64];
extern const void* gadget_ld8u_sh8_imm[16][16][64];
extern const void* gadget_ld8u_neg_imm[16][16][64];
extern const void* gadget_ld16u[16][16];
extern const void* gadget_ld16u_imm[16][16][64];
extern const void* gadget_ld16u_sh8_imm[16][16][64];
extern const void* gadget_ld16u_neg_imm[16][16][64];
extern const void* gadget_ld32u[16][16];
extern const void* gadget_ld32u_imm[16][16][64];
extern const void* gadget_ld32u_sh8_imm[16][16][64];
extern const void* gadget_ld32u_neg_imm[16][16][64];
extern const void* gadget_ld_i64[16][16];
extern const void* gadget_ld_i64_imm[16][16][64];
extern const void* gadget_ld_i64_sh8_imm[16][16][64];
extern const void* gadget_ld_i64_neg_imm[16][16][64];
