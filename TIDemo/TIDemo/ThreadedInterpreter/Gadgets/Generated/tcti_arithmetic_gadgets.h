/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_add_i32[16][16][16];
extern const void* gadget_add_i64[16][16][16];
extern const void* gadget_sub_i32[16][16][16];
extern const void* gadget_sub_i64[16][16][16];
extern const void* gadget_mul_i32[16][16][16];
extern const void* gadget_mul_i64[16][16][16];
extern const void* gadget_div_i32[16][16][16];
extern const void* gadget_div_i64[16][16][16];
extern const void* gadget_divu_i32[16][16][16];
extern const void* gadget_divu_i64[16][16][16];
extern const void* gadget_rem_i32[16][16][16];
extern const void* gadget_rem_i64[16][16][16];
extern const void* gadget_remu_i32[16][16][16];
extern const void* gadget_remu_i64[16][16][16];
