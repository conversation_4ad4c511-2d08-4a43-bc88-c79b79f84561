/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_ld8s_i32[16][16];
extern const void* gadget_ld8s_i32_imm[16][16][64];
extern const void* gadget_ld8s_i32_sh8_imm[16][16][64];
extern const void* gadget_ld8s_i32_neg_imm[16][16][64];
extern const void* gadget_ld8s_i64[16][16];
extern const void* gadget_ld8s_i64_imm[16][16][64];
extern const void* gadget_ld8s_i64_sh8_imm[16][16][64];
extern const void* gadget_ld8s_i64_neg_imm[16][16][64];
extern const void* gadget_ld16s_i32[16][16];
extern const void* gadget_ld16s_i32_imm[16][16][64];
extern const void* gadget_ld16s_i32_sh8_imm[16][16][64];
extern const void* gadget_ld16s_i32_neg_imm[16][16][64];
extern const void* gadget_ld16s_i64[16][16];
extern const void* gadget_ld16s_i64_imm[16][16][64];
extern const void* gadget_ld16s_i64_sh8_imm[16][16][64];
extern const void* gadget_ld16s_i64_neg_imm[16][16][64];
extern const void* gadget_ld32s_i64[16][16];
extern const void* gadget_ld32s_i64_imm[16][16][64];
extern const void* gadget_ld32s_i64_sh8_imm[16][16][64];
extern const void* gadget_ld32s_i64_neg_imm[16][16][64];
