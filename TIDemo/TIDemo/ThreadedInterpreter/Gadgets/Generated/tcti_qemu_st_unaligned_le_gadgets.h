/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_qemu_st_ub_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_st_leuw_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_st_leul_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off32_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off32_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off48_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off48_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off64_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off64_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off96_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off96_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off128_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_off128_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off32_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off32_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off48_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off48_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off64_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off64_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off96_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off96_i64[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off128_i32[16][16];
extern const void* gadget_qemu_st_ub_unaligned_mode02_off128_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off32_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off32_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off48_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off48_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off64_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off64_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off96_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off96_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off128_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode32_off128_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off32_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off32_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off48_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off48_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off64_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off64_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off96_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off96_i64[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off128_i32[16][16];
extern const void* gadget_qemu_st_leq_unaligned_mode3a_off128_i64[16][16];
