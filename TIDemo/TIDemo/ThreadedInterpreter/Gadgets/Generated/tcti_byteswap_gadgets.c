/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#include "tcti_byteswap_gadgets.h"

static __attribute__((naked)) void gadget_bswap16_arg0_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg0_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w0, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg1_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w1, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg2_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w2, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg3_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w3, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg4_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w4, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg5_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w5, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg6_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w6, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg7_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w7, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg8_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w8, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg9_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w9, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg10_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w10, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg11_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w11, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg12_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w12, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg13_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w13, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg14_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w14, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg0(void)
{
	asm(
		"rev w27, w0 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg1(void)
{
	asm(
		"rev w27, w1 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg2(void)
{
	asm(
		"rev w27, w2 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg3(void)
{
	asm(
		"rev w27, w3 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg4(void)
{
	asm(
		"rev w27, w4 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg5(void)
{
	asm(
		"rev w27, w5 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg6(void)
{
	asm(
		"rev w27, w6 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg7(void)
{
	asm(
		"rev w27, w7 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg8(void)
{
	asm(
		"rev w27, w8 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg9(void)
{
	asm(
		"rev w27, w9 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg10(void)
{
	asm(
		"rev w27, w10 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg11(void)
{
	asm(
		"rev w27, w11 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg12(void)
{
	asm(
		"rev w27, w12 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg13(void)
{
	asm(
		"rev w27, w13 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg14(void)
{
	asm(
		"rev w27, w14 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap16_arg15_arg15(void)
{
	asm(
		"rev w27, w15 \n"
		"lsr w15, w27, #16 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

const void* gadget_bswap16[16][16] = {
		{gadget_bswap16_arg0_arg0, gadget_bswap16_arg0_arg1, gadget_bswap16_arg0_arg2, gadget_bswap16_arg0_arg3, gadget_bswap16_arg0_arg4, gadget_bswap16_arg0_arg5, gadget_bswap16_arg0_arg6, gadget_bswap16_arg0_arg7, gadget_bswap16_arg0_arg8, gadget_bswap16_arg0_arg9, gadget_bswap16_arg0_arg10, gadget_bswap16_arg0_arg11, gadget_bswap16_arg0_arg12, gadget_bswap16_arg0_arg13, gadget_bswap16_arg0_arg14, gadget_bswap16_arg0_arg15, },
		{gadget_bswap16_arg1_arg0, gadget_bswap16_arg1_arg1, gadget_bswap16_arg1_arg2, gadget_bswap16_arg1_arg3, gadget_bswap16_arg1_arg4, gadget_bswap16_arg1_arg5, gadget_bswap16_arg1_arg6, gadget_bswap16_arg1_arg7, gadget_bswap16_arg1_arg8, gadget_bswap16_arg1_arg9, gadget_bswap16_arg1_arg10, gadget_bswap16_arg1_arg11, gadget_bswap16_arg1_arg12, gadget_bswap16_arg1_arg13, gadget_bswap16_arg1_arg14, gadget_bswap16_arg1_arg15, },
		{gadget_bswap16_arg2_arg0, gadget_bswap16_arg2_arg1, gadget_bswap16_arg2_arg2, gadget_bswap16_arg2_arg3, gadget_bswap16_arg2_arg4, gadget_bswap16_arg2_arg5, gadget_bswap16_arg2_arg6, gadget_bswap16_arg2_arg7, gadget_bswap16_arg2_arg8, gadget_bswap16_arg2_arg9, gadget_bswap16_arg2_arg10, gadget_bswap16_arg2_arg11, gadget_bswap16_arg2_arg12, gadget_bswap16_arg2_arg13, gadget_bswap16_arg2_arg14, gadget_bswap16_arg2_arg15, },
		{gadget_bswap16_arg3_arg0, gadget_bswap16_arg3_arg1, gadget_bswap16_arg3_arg2, gadget_bswap16_arg3_arg3, gadget_bswap16_arg3_arg4, gadget_bswap16_arg3_arg5, gadget_bswap16_arg3_arg6, gadget_bswap16_arg3_arg7, gadget_bswap16_arg3_arg8, gadget_bswap16_arg3_arg9, gadget_bswap16_arg3_arg10, gadget_bswap16_arg3_arg11, gadget_bswap16_arg3_arg12, gadget_bswap16_arg3_arg13, gadget_bswap16_arg3_arg14, gadget_bswap16_arg3_arg15, },
		{gadget_bswap16_arg4_arg0, gadget_bswap16_arg4_arg1, gadget_bswap16_arg4_arg2, gadget_bswap16_arg4_arg3, gadget_bswap16_arg4_arg4, gadget_bswap16_arg4_arg5, gadget_bswap16_arg4_arg6, gadget_bswap16_arg4_arg7, gadget_bswap16_arg4_arg8, gadget_bswap16_arg4_arg9, gadget_bswap16_arg4_arg10, gadget_bswap16_arg4_arg11, gadget_bswap16_arg4_arg12, gadget_bswap16_arg4_arg13, gadget_bswap16_arg4_arg14, gadget_bswap16_arg4_arg15, },
		{gadget_bswap16_arg5_arg0, gadget_bswap16_arg5_arg1, gadget_bswap16_arg5_arg2, gadget_bswap16_arg5_arg3, gadget_bswap16_arg5_arg4, gadget_bswap16_arg5_arg5, gadget_bswap16_arg5_arg6, gadget_bswap16_arg5_arg7, gadget_bswap16_arg5_arg8, gadget_bswap16_arg5_arg9, gadget_bswap16_arg5_arg10, gadget_bswap16_arg5_arg11, gadget_bswap16_arg5_arg12, gadget_bswap16_arg5_arg13, gadget_bswap16_arg5_arg14, gadget_bswap16_arg5_arg15, },
		{gadget_bswap16_arg6_arg0, gadget_bswap16_arg6_arg1, gadget_bswap16_arg6_arg2, gadget_bswap16_arg6_arg3, gadget_bswap16_arg6_arg4, gadget_bswap16_arg6_arg5, gadget_bswap16_arg6_arg6, gadget_bswap16_arg6_arg7, gadget_bswap16_arg6_arg8, gadget_bswap16_arg6_arg9, gadget_bswap16_arg6_arg10, gadget_bswap16_arg6_arg11, gadget_bswap16_arg6_arg12, gadget_bswap16_arg6_arg13, gadget_bswap16_arg6_arg14, gadget_bswap16_arg6_arg15, },
		{gadget_bswap16_arg7_arg0, gadget_bswap16_arg7_arg1, gadget_bswap16_arg7_arg2, gadget_bswap16_arg7_arg3, gadget_bswap16_arg7_arg4, gadget_bswap16_arg7_arg5, gadget_bswap16_arg7_arg6, gadget_bswap16_arg7_arg7, gadget_bswap16_arg7_arg8, gadget_bswap16_arg7_arg9, gadget_bswap16_arg7_arg10, gadget_bswap16_arg7_arg11, gadget_bswap16_arg7_arg12, gadget_bswap16_arg7_arg13, gadget_bswap16_arg7_arg14, gadget_bswap16_arg7_arg15, },
		{gadget_bswap16_arg8_arg0, gadget_bswap16_arg8_arg1, gadget_bswap16_arg8_arg2, gadget_bswap16_arg8_arg3, gadget_bswap16_arg8_arg4, gadget_bswap16_arg8_arg5, gadget_bswap16_arg8_arg6, gadget_bswap16_arg8_arg7, gadget_bswap16_arg8_arg8, gadget_bswap16_arg8_arg9, gadget_bswap16_arg8_arg10, gadget_bswap16_arg8_arg11, gadget_bswap16_arg8_arg12, gadget_bswap16_arg8_arg13, gadget_bswap16_arg8_arg14, gadget_bswap16_arg8_arg15, },
		{gadget_bswap16_arg9_arg0, gadget_bswap16_arg9_arg1, gadget_bswap16_arg9_arg2, gadget_bswap16_arg9_arg3, gadget_bswap16_arg9_arg4, gadget_bswap16_arg9_arg5, gadget_bswap16_arg9_arg6, gadget_bswap16_arg9_arg7, gadget_bswap16_arg9_arg8, gadget_bswap16_arg9_arg9, gadget_bswap16_arg9_arg10, gadget_bswap16_arg9_arg11, gadget_bswap16_arg9_arg12, gadget_bswap16_arg9_arg13, gadget_bswap16_arg9_arg14, gadget_bswap16_arg9_arg15, },
		{gadget_bswap16_arg10_arg0, gadget_bswap16_arg10_arg1, gadget_bswap16_arg10_arg2, gadget_bswap16_arg10_arg3, gadget_bswap16_arg10_arg4, gadget_bswap16_arg10_arg5, gadget_bswap16_arg10_arg6, gadget_bswap16_arg10_arg7, gadget_bswap16_arg10_arg8, gadget_bswap16_arg10_arg9, gadget_bswap16_arg10_arg10, gadget_bswap16_arg10_arg11, gadget_bswap16_arg10_arg12, gadget_bswap16_arg10_arg13, gadget_bswap16_arg10_arg14, gadget_bswap16_arg10_arg15, },
		{gadget_bswap16_arg11_arg0, gadget_bswap16_arg11_arg1, gadget_bswap16_arg11_arg2, gadget_bswap16_arg11_arg3, gadget_bswap16_arg11_arg4, gadget_bswap16_arg11_arg5, gadget_bswap16_arg11_arg6, gadget_bswap16_arg11_arg7, gadget_bswap16_arg11_arg8, gadget_bswap16_arg11_arg9, gadget_bswap16_arg11_arg10, gadget_bswap16_arg11_arg11, gadget_bswap16_arg11_arg12, gadget_bswap16_arg11_arg13, gadget_bswap16_arg11_arg14, gadget_bswap16_arg11_arg15, },
		{gadget_bswap16_arg12_arg0, gadget_bswap16_arg12_arg1, gadget_bswap16_arg12_arg2, gadget_bswap16_arg12_arg3, gadget_bswap16_arg12_arg4, gadget_bswap16_arg12_arg5, gadget_bswap16_arg12_arg6, gadget_bswap16_arg12_arg7, gadget_bswap16_arg12_arg8, gadget_bswap16_arg12_arg9, gadget_bswap16_arg12_arg10, gadget_bswap16_arg12_arg11, gadget_bswap16_arg12_arg12, gadget_bswap16_arg12_arg13, gadget_bswap16_arg12_arg14, gadget_bswap16_arg12_arg15, },
		{gadget_bswap16_arg13_arg0, gadget_bswap16_arg13_arg1, gadget_bswap16_arg13_arg2, gadget_bswap16_arg13_arg3, gadget_bswap16_arg13_arg4, gadget_bswap16_arg13_arg5, gadget_bswap16_arg13_arg6, gadget_bswap16_arg13_arg7, gadget_bswap16_arg13_arg8, gadget_bswap16_arg13_arg9, gadget_bswap16_arg13_arg10, gadget_bswap16_arg13_arg11, gadget_bswap16_arg13_arg12, gadget_bswap16_arg13_arg13, gadget_bswap16_arg13_arg14, gadget_bswap16_arg13_arg15, },
		{gadget_bswap16_arg14_arg0, gadget_bswap16_arg14_arg1, gadget_bswap16_arg14_arg2, gadget_bswap16_arg14_arg3, gadget_bswap16_arg14_arg4, gadget_bswap16_arg14_arg5, gadget_bswap16_arg14_arg6, gadget_bswap16_arg14_arg7, gadget_bswap16_arg14_arg8, gadget_bswap16_arg14_arg9, gadget_bswap16_arg14_arg10, gadget_bswap16_arg14_arg11, gadget_bswap16_arg14_arg12, gadget_bswap16_arg14_arg13, gadget_bswap16_arg14_arg14, gadget_bswap16_arg14_arg15, },
		{gadget_bswap16_arg15_arg0, gadget_bswap16_arg15_arg1, gadget_bswap16_arg15_arg2, gadget_bswap16_arg15_arg3, gadget_bswap16_arg15_arg4, gadget_bswap16_arg15_arg5, gadget_bswap16_arg15_arg6, gadget_bswap16_arg15_arg7, gadget_bswap16_arg15_arg8, gadget_bswap16_arg15_arg9, gadget_bswap16_arg15_arg10, gadget_bswap16_arg15_arg11, gadget_bswap16_arg15_arg12, gadget_bswap16_arg15_arg13, gadget_bswap16_arg15_arg14, gadget_bswap16_arg15_arg15, },
};
static __attribute__((naked)) void gadget_bswap32_arg0_arg0(void)
{
	asm(
		"rev w0, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg1(void)
{
	asm(
		"rev w0, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg2(void)
{
	asm(
		"rev w0, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg3(void)
{
	asm(
		"rev w0, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg4(void)
{
	asm(
		"rev w0, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg5(void)
{
	asm(
		"rev w0, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg6(void)
{
	asm(
		"rev w0, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg7(void)
{
	asm(
		"rev w0, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg8(void)
{
	asm(
		"rev w0, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg9(void)
{
	asm(
		"rev w0, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg10(void)
{
	asm(
		"rev w0, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg11(void)
{
	asm(
		"rev w0, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg12(void)
{
	asm(
		"rev w0, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg13(void)
{
	asm(
		"rev w0, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg14(void)
{
	asm(
		"rev w0, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg0_arg15(void)
{
	asm(
		"rev w0, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg0(void)
{
	asm(
		"rev w1, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg1(void)
{
	asm(
		"rev w1, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg2(void)
{
	asm(
		"rev w1, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg3(void)
{
	asm(
		"rev w1, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg4(void)
{
	asm(
		"rev w1, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg5(void)
{
	asm(
		"rev w1, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg6(void)
{
	asm(
		"rev w1, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg7(void)
{
	asm(
		"rev w1, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg8(void)
{
	asm(
		"rev w1, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg9(void)
{
	asm(
		"rev w1, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg10(void)
{
	asm(
		"rev w1, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg11(void)
{
	asm(
		"rev w1, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg12(void)
{
	asm(
		"rev w1, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg13(void)
{
	asm(
		"rev w1, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg14(void)
{
	asm(
		"rev w1, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg1_arg15(void)
{
	asm(
		"rev w1, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg0(void)
{
	asm(
		"rev w2, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg1(void)
{
	asm(
		"rev w2, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg2(void)
{
	asm(
		"rev w2, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg3(void)
{
	asm(
		"rev w2, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg4(void)
{
	asm(
		"rev w2, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg5(void)
{
	asm(
		"rev w2, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg6(void)
{
	asm(
		"rev w2, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg7(void)
{
	asm(
		"rev w2, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg8(void)
{
	asm(
		"rev w2, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg9(void)
{
	asm(
		"rev w2, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg10(void)
{
	asm(
		"rev w2, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg11(void)
{
	asm(
		"rev w2, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg12(void)
{
	asm(
		"rev w2, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg13(void)
{
	asm(
		"rev w2, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg14(void)
{
	asm(
		"rev w2, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg2_arg15(void)
{
	asm(
		"rev w2, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg0(void)
{
	asm(
		"rev w3, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg1(void)
{
	asm(
		"rev w3, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg2(void)
{
	asm(
		"rev w3, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg3(void)
{
	asm(
		"rev w3, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg4(void)
{
	asm(
		"rev w3, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg5(void)
{
	asm(
		"rev w3, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg6(void)
{
	asm(
		"rev w3, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg7(void)
{
	asm(
		"rev w3, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg8(void)
{
	asm(
		"rev w3, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg9(void)
{
	asm(
		"rev w3, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg10(void)
{
	asm(
		"rev w3, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg11(void)
{
	asm(
		"rev w3, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg12(void)
{
	asm(
		"rev w3, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg13(void)
{
	asm(
		"rev w3, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg14(void)
{
	asm(
		"rev w3, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg3_arg15(void)
{
	asm(
		"rev w3, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg0(void)
{
	asm(
		"rev w4, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg1(void)
{
	asm(
		"rev w4, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg2(void)
{
	asm(
		"rev w4, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg3(void)
{
	asm(
		"rev w4, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg4(void)
{
	asm(
		"rev w4, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg5(void)
{
	asm(
		"rev w4, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg6(void)
{
	asm(
		"rev w4, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg7(void)
{
	asm(
		"rev w4, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg8(void)
{
	asm(
		"rev w4, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg9(void)
{
	asm(
		"rev w4, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg10(void)
{
	asm(
		"rev w4, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg11(void)
{
	asm(
		"rev w4, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg12(void)
{
	asm(
		"rev w4, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg13(void)
{
	asm(
		"rev w4, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg14(void)
{
	asm(
		"rev w4, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg4_arg15(void)
{
	asm(
		"rev w4, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg0(void)
{
	asm(
		"rev w5, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg1(void)
{
	asm(
		"rev w5, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg2(void)
{
	asm(
		"rev w5, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg3(void)
{
	asm(
		"rev w5, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg4(void)
{
	asm(
		"rev w5, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg5(void)
{
	asm(
		"rev w5, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg6(void)
{
	asm(
		"rev w5, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg7(void)
{
	asm(
		"rev w5, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg8(void)
{
	asm(
		"rev w5, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg9(void)
{
	asm(
		"rev w5, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg10(void)
{
	asm(
		"rev w5, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg11(void)
{
	asm(
		"rev w5, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg12(void)
{
	asm(
		"rev w5, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg13(void)
{
	asm(
		"rev w5, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg14(void)
{
	asm(
		"rev w5, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg5_arg15(void)
{
	asm(
		"rev w5, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg0(void)
{
	asm(
		"rev w6, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg1(void)
{
	asm(
		"rev w6, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg2(void)
{
	asm(
		"rev w6, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg3(void)
{
	asm(
		"rev w6, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg4(void)
{
	asm(
		"rev w6, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg5(void)
{
	asm(
		"rev w6, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg6(void)
{
	asm(
		"rev w6, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg7(void)
{
	asm(
		"rev w6, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg8(void)
{
	asm(
		"rev w6, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg9(void)
{
	asm(
		"rev w6, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg10(void)
{
	asm(
		"rev w6, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg11(void)
{
	asm(
		"rev w6, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg12(void)
{
	asm(
		"rev w6, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg13(void)
{
	asm(
		"rev w6, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg14(void)
{
	asm(
		"rev w6, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg6_arg15(void)
{
	asm(
		"rev w6, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg0(void)
{
	asm(
		"rev w7, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg1(void)
{
	asm(
		"rev w7, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg2(void)
{
	asm(
		"rev w7, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg3(void)
{
	asm(
		"rev w7, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg4(void)
{
	asm(
		"rev w7, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg5(void)
{
	asm(
		"rev w7, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg6(void)
{
	asm(
		"rev w7, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg7(void)
{
	asm(
		"rev w7, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg8(void)
{
	asm(
		"rev w7, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg9(void)
{
	asm(
		"rev w7, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg10(void)
{
	asm(
		"rev w7, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg11(void)
{
	asm(
		"rev w7, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg12(void)
{
	asm(
		"rev w7, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg13(void)
{
	asm(
		"rev w7, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg14(void)
{
	asm(
		"rev w7, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg7_arg15(void)
{
	asm(
		"rev w7, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg0(void)
{
	asm(
		"rev w8, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg1(void)
{
	asm(
		"rev w8, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg2(void)
{
	asm(
		"rev w8, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg3(void)
{
	asm(
		"rev w8, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg4(void)
{
	asm(
		"rev w8, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg5(void)
{
	asm(
		"rev w8, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg6(void)
{
	asm(
		"rev w8, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg7(void)
{
	asm(
		"rev w8, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg8(void)
{
	asm(
		"rev w8, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg9(void)
{
	asm(
		"rev w8, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg10(void)
{
	asm(
		"rev w8, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg11(void)
{
	asm(
		"rev w8, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg12(void)
{
	asm(
		"rev w8, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg13(void)
{
	asm(
		"rev w8, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg14(void)
{
	asm(
		"rev w8, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg8_arg15(void)
{
	asm(
		"rev w8, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg0(void)
{
	asm(
		"rev w9, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg1(void)
{
	asm(
		"rev w9, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg2(void)
{
	asm(
		"rev w9, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg3(void)
{
	asm(
		"rev w9, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg4(void)
{
	asm(
		"rev w9, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg5(void)
{
	asm(
		"rev w9, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg6(void)
{
	asm(
		"rev w9, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg7(void)
{
	asm(
		"rev w9, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg8(void)
{
	asm(
		"rev w9, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg9(void)
{
	asm(
		"rev w9, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg10(void)
{
	asm(
		"rev w9, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg11(void)
{
	asm(
		"rev w9, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg12(void)
{
	asm(
		"rev w9, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg13(void)
{
	asm(
		"rev w9, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg14(void)
{
	asm(
		"rev w9, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg9_arg15(void)
{
	asm(
		"rev w9, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg0(void)
{
	asm(
		"rev w10, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg1(void)
{
	asm(
		"rev w10, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg2(void)
{
	asm(
		"rev w10, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg3(void)
{
	asm(
		"rev w10, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg4(void)
{
	asm(
		"rev w10, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg5(void)
{
	asm(
		"rev w10, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg6(void)
{
	asm(
		"rev w10, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg7(void)
{
	asm(
		"rev w10, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg8(void)
{
	asm(
		"rev w10, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg9(void)
{
	asm(
		"rev w10, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg10(void)
{
	asm(
		"rev w10, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg11(void)
{
	asm(
		"rev w10, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg12(void)
{
	asm(
		"rev w10, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg13(void)
{
	asm(
		"rev w10, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg14(void)
{
	asm(
		"rev w10, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg10_arg15(void)
{
	asm(
		"rev w10, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg0(void)
{
	asm(
		"rev w11, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg1(void)
{
	asm(
		"rev w11, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg2(void)
{
	asm(
		"rev w11, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg3(void)
{
	asm(
		"rev w11, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg4(void)
{
	asm(
		"rev w11, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg5(void)
{
	asm(
		"rev w11, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg6(void)
{
	asm(
		"rev w11, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg7(void)
{
	asm(
		"rev w11, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg8(void)
{
	asm(
		"rev w11, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg9(void)
{
	asm(
		"rev w11, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg10(void)
{
	asm(
		"rev w11, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg11(void)
{
	asm(
		"rev w11, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg12(void)
{
	asm(
		"rev w11, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg13(void)
{
	asm(
		"rev w11, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg14(void)
{
	asm(
		"rev w11, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg11_arg15(void)
{
	asm(
		"rev w11, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg0(void)
{
	asm(
		"rev w12, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg1(void)
{
	asm(
		"rev w12, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg2(void)
{
	asm(
		"rev w12, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg3(void)
{
	asm(
		"rev w12, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg4(void)
{
	asm(
		"rev w12, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg5(void)
{
	asm(
		"rev w12, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg6(void)
{
	asm(
		"rev w12, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg7(void)
{
	asm(
		"rev w12, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg8(void)
{
	asm(
		"rev w12, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg9(void)
{
	asm(
		"rev w12, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg10(void)
{
	asm(
		"rev w12, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg11(void)
{
	asm(
		"rev w12, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg12(void)
{
	asm(
		"rev w12, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg13(void)
{
	asm(
		"rev w12, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg14(void)
{
	asm(
		"rev w12, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg12_arg15(void)
{
	asm(
		"rev w12, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg0(void)
{
	asm(
		"rev w13, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg1(void)
{
	asm(
		"rev w13, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg2(void)
{
	asm(
		"rev w13, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg3(void)
{
	asm(
		"rev w13, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg4(void)
{
	asm(
		"rev w13, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg5(void)
{
	asm(
		"rev w13, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg6(void)
{
	asm(
		"rev w13, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg7(void)
{
	asm(
		"rev w13, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg8(void)
{
	asm(
		"rev w13, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg9(void)
{
	asm(
		"rev w13, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg10(void)
{
	asm(
		"rev w13, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg11(void)
{
	asm(
		"rev w13, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg12(void)
{
	asm(
		"rev w13, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg13(void)
{
	asm(
		"rev w13, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg14(void)
{
	asm(
		"rev w13, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg13_arg15(void)
{
	asm(
		"rev w13, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg0(void)
{
	asm(
		"rev w14, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg1(void)
{
	asm(
		"rev w14, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg2(void)
{
	asm(
		"rev w14, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg3(void)
{
	asm(
		"rev w14, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg4(void)
{
	asm(
		"rev w14, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg5(void)
{
	asm(
		"rev w14, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg6(void)
{
	asm(
		"rev w14, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg7(void)
{
	asm(
		"rev w14, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg8(void)
{
	asm(
		"rev w14, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg9(void)
{
	asm(
		"rev w14, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg10(void)
{
	asm(
		"rev w14, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg11(void)
{
	asm(
		"rev w14, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg12(void)
{
	asm(
		"rev w14, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg13(void)
{
	asm(
		"rev w14, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg14(void)
{
	asm(
		"rev w14, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg14_arg15(void)
{
	asm(
		"rev w14, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg0(void)
{
	asm(
		"rev w15, w0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg1(void)
{
	asm(
		"rev w15, w1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg2(void)
{
	asm(
		"rev w15, w2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg3(void)
{
	asm(
		"rev w15, w3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg4(void)
{
	asm(
		"rev w15, w4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg5(void)
{
	asm(
		"rev w15, w5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg6(void)
{
	asm(
		"rev w15, w6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg7(void)
{
	asm(
		"rev w15, w7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg8(void)
{
	asm(
		"rev w15, w8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg9(void)
{
	asm(
		"rev w15, w9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg10(void)
{
	asm(
		"rev w15, w10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg11(void)
{
	asm(
		"rev w15, w11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg12(void)
{
	asm(
		"rev w15, w12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg13(void)
{
	asm(
		"rev w15, w13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg14(void)
{
	asm(
		"rev w15, w14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap32_arg15_arg15(void)
{
	asm(
		"rev w15, w15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

const void* gadget_bswap32[16][16] = {
		{gadget_bswap32_arg0_arg0, gadget_bswap32_arg0_arg1, gadget_bswap32_arg0_arg2, gadget_bswap32_arg0_arg3, gadget_bswap32_arg0_arg4, gadget_bswap32_arg0_arg5, gadget_bswap32_arg0_arg6, gadget_bswap32_arg0_arg7, gadget_bswap32_arg0_arg8, gadget_bswap32_arg0_arg9, gadget_bswap32_arg0_arg10, gadget_bswap32_arg0_arg11, gadget_bswap32_arg0_arg12, gadget_bswap32_arg0_arg13, gadget_bswap32_arg0_arg14, gadget_bswap32_arg0_arg15, },
		{gadget_bswap32_arg1_arg0, gadget_bswap32_arg1_arg1, gadget_bswap32_arg1_arg2, gadget_bswap32_arg1_arg3, gadget_bswap32_arg1_arg4, gadget_bswap32_arg1_arg5, gadget_bswap32_arg1_arg6, gadget_bswap32_arg1_arg7, gadget_bswap32_arg1_arg8, gadget_bswap32_arg1_arg9, gadget_bswap32_arg1_arg10, gadget_bswap32_arg1_arg11, gadget_bswap32_arg1_arg12, gadget_bswap32_arg1_arg13, gadget_bswap32_arg1_arg14, gadget_bswap32_arg1_arg15, },
		{gadget_bswap32_arg2_arg0, gadget_bswap32_arg2_arg1, gadget_bswap32_arg2_arg2, gadget_bswap32_arg2_arg3, gadget_bswap32_arg2_arg4, gadget_bswap32_arg2_arg5, gadget_bswap32_arg2_arg6, gadget_bswap32_arg2_arg7, gadget_bswap32_arg2_arg8, gadget_bswap32_arg2_arg9, gadget_bswap32_arg2_arg10, gadget_bswap32_arg2_arg11, gadget_bswap32_arg2_arg12, gadget_bswap32_arg2_arg13, gadget_bswap32_arg2_arg14, gadget_bswap32_arg2_arg15, },
		{gadget_bswap32_arg3_arg0, gadget_bswap32_arg3_arg1, gadget_bswap32_arg3_arg2, gadget_bswap32_arg3_arg3, gadget_bswap32_arg3_arg4, gadget_bswap32_arg3_arg5, gadget_bswap32_arg3_arg6, gadget_bswap32_arg3_arg7, gadget_bswap32_arg3_arg8, gadget_bswap32_arg3_arg9, gadget_bswap32_arg3_arg10, gadget_bswap32_arg3_arg11, gadget_bswap32_arg3_arg12, gadget_bswap32_arg3_arg13, gadget_bswap32_arg3_arg14, gadget_bswap32_arg3_arg15, },
		{gadget_bswap32_arg4_arg0, gadget_bswap32_arg4_arg1, gadget_bswap32_arg4_arg2, gadget_bswap32_arg4_arg3, gadget_bswap32_arg4_arg4, gadget_bswap32_arg4_arg5, gadget_bswap32_arg4_arg6, gadget_bswap32_arg4_arg7, gadget_bswap32_arg4_arg8, gadget_bswap32_arg4_arg9, gadget_bswap32_arg4_arg10, gadget_bswap32_arg4_arg11, gadget_bswap32_arg4_arg12, gadget_bswap32_arg4_arg13, gadget_bswap32_arg4_arg14, gadget_bswap32_arg4_arg15, },
		{gadget_bswap32_arg5_arg0, gadget_bswap32_arg5_arg1, gadget_bswap32_arg5_arg2, gadget_bswap32_arg5_arg3, gadget_bswap32_arg5_arg4, gadget_bswap32_arg5_arg5, gadget_bswap32_arg5_arg6, gadget_bswap32_arg5_arg7, gadget_bswap32_arg5_arg8, gadget_bswap32_arg5_arg9, gadget_bswap32_arg5_arg10, gadget_bswap32_arg5_arg11, gadget_bswap32_arg5_arg12, gadget_bswap32_arg5_arg13, gadget_bswap32_arg5_arg14, gadget_bswap32_arg5_arg15, },
		{gadget_bswap32_arg6_arg0, gadget_bswap32_arg6_arg1, gadget_bswap32_arg6_arg2, gadget_bswap32_arg6_arg3, gadget_bswap32_arg6_arg4, gadget_bswap32_arg6_arg5, gadget_bswap32_arg6_arg6, gadget_bswap32_arg6_arg7, gadget_bswap32_arg6_arg8, gadget_bswap32_arg6_arg9, gadget_bswap32_arg6_arg10, gadget_bswap32_arg6_arg11, gadget_bswap32_arg6_arg12, gadget_bswap32_arg6_arg13, gadget_bswap32_arg6_arg14, gadget_bswap32_arg6_arg15, },
		{gadget_bswap32_arg7_arg0, gadget_bswap32_arg7_arg1, gadget_bswap32_arg7_arg2, gadget_bswap32_arg7_arg3, gadget_bswap32_arg7_arg4, gadget_bswap32_arg7_arg5, gadget_bswap32_arg7_arg6, gadget_bswap32_arg7_arg7, gadget_bswap32_arg7_arg8, gadget_bswap32_arg7_arg9, gadget_bswap32_arg7_arg10, gadget_bswap32_arg7_arg11, gadget_bswap32_arg7_arg12, gadget_bswap32_arg7_arg13, gadget_bswap32_arg7_arg14, gadget_bswap32_arg7_arg15, },
		{gadget_bswap32_arg8_arg0, gadget_bswap32_arg8_arg1, gadget_bswap32_arg8_arg2, gadget_bswap32_arg8_arg3, gadget_bswap32_arg8_arg4, gadget_bswap32_arg8_arg5, gadget_bswap32_arg8_arg6, gadget_bswap32_arg8_arg7, gadget_bswap32_arg8_arg8, gadget_bswap32_arg8_arg9, gadget_bswap32_arg8_arg10, gadget_bswap32_arg8_arg11, gadget_bswap32_arg8_arg12, gadget_bswap32_arg8_arg13, gadget_bswap32_arg8_arg14, gadget_bswap32_arg8_arg15, },
		{gadget_bswap32_arg9_arg0, gadget_bswap32_arg9_arg1, gadget_bswap32_arg9_arg2, gadget_bswap32_arg9_arg3, gadget_bswap32_arg9_arg4, gadget_bswap32_arg9_arg5, gadget_bswap32_arg9_arg6, gadget_bswap32_arg9_arg7, gadget_bswap32_arg9_arg8, gadget_bswap32_arg9_arg9, gadget_bswap32_arg9_arg10, gadget_bswap32_arg9_arg11, gadget_bswap32_arg9_arg12, gadget_bswap32_arg9_arg13, gadget_bswap32_arg9_arg14, gadget_bswap32_arg9_arg15, },
		{gadget_bswap32_arg10_arg0, gadget_bswap32_arg10_arg1, gadget_bswap32_arg10_arg2, gadget_bswap32_arg10_arg3, gadget_bswap32_arg10_arg4, gadget_bswap32_arg10_arg5, gadget_bswap32_arg10_arg6, gadget_bswap32_arg10_arg7, gadget_bswap32_arg10_arg8, gadget_bswap32_arg10_arg9, gadget_bswap32_arg10_arg10, gadget_bswap32_arg10_arg11, gadget_bswap32_arg10_arg12, gadget_bswap32_arg10_arg13, gadget_bswap32_arg10_arg14, gadget_bswap32_arg10_arg15, },
		{gadget_bswap32_arg11_arg0, gadget_bswap32_arg11_arg1, gadget_bswap32_arg11_arg2, gadget_bswap32_arg11_arg3, gadget_bswap32_arg11_arg4, gadget_bswap32_arg11_arg5, gadget_bswap32_arg11_arg6, gadget_bswap32_arg11_arg7, gadget_bswap32_arg11_arg8, gadget_bswap32_arg11_arg9, gadget_bswap32_arg11_arg10, gadget_bswap32_arg11_arg11, gadget_bswap32_arg11_arg12, gadget_bswap32_arg11_arg13, gadget_bswap32_arg11_arg14, gadget_bswap32_arg11_arg15, },
		{gadget_bswap32_arg12_arg0, gadget_bswap32_arg12_arg1, gadget_bswap32_arg12_arg2, gadget_bswap32_arg12_arg3, gadget_bswap32_arg12_arg4, gadget_bswap32_arg12_arg5, gadget_bswap32_arg12_arg6, gadget_bswap32_arg12_arg7, gadget_bswap32_arg12_arg8, gadget_bswap32_arg12_arg9, gadget_bswap32_arg12_arg10, gadget_bswap32_arg12_arg11, gadget_bswap32_arg12_arg12, gadget_bswap32_arg12_arg13, gadget_bswap32_arg12_arg14, gadget_bswap32_arg12_arg15, },
		{gadget_bswap32_arg13_arg0, gadget_bswap32_arg13_arg1, gadget_bswap32_arg13_arg2, gadget_bswap32_arg13_arg3, gadget_bswap32_arg13_arg4, gadget_bswap32_arg13_arg5, gadget_bswap32_arg13_arg6, gadget_bswap32_arg13_arg7, gadget_bswap32_arg13_arg8, gadget_bswap32_arg13_arg9, gadget_bswap32_arg13_arg10, gadget_bswap32_arg13_arg11, gadget_bswap32_arg13_arg12, gadget_bswap32_arg13_arg13, gadget_bswap32_arg13_arg14, gadget_bswap32_arg13_arg15, },
		{gadget_bswap32_arg14_arg0, gadget_bswap32_arg14_arg1, gadget_bswap32_arg14_arg2, gadget_bswap32_arg14_arg3, gadget_bswap32_arg14_arg4, gadget_bswap32_arg14_arg5, gadget_bswap32_arg14_arg6, gadget_bswap32_arg14_arg7, gadget_bswap32_arg14_arg8, gadget_bswap32_arg14_arg9, gadget_bswap32_arg14_arg10, gadget_bswap32_arg14_arg11, gadget_bswap32_arg14_arg12, gadget_bswap32_arg14_arg13, gadget_bswap32_arg14_arg14, gadget_bswap32_arg14_arg15, },
		{gadget_bswap32_arg15_arg0, gadget_bswap32_arg15_arg1, gadget_bswap32_arg15_arg2, gadget_bswap32_arg15_arg3, gadget_bswap32_arg15_arg4, gadget_bswap32_arg15_arg5, gadget_bswap32_arg15_arg6, gadget_bswap32_arg15_arg7, gadget_bswap32_arg15_arg8, gadget_bswap32_arg15_arg9, gadget_bswap32_arg15_arg10, gadget_bswap32_arg15_arg11, gadget_bswap32_arg15_arg12, gadget_bswap32_arg15_arg13, gadget_bswap32_arg15_arg14, gadget_bswap32_arg15_arg15, },
};
static __attribute__((naked)) void gadget_bswap64_arg0_arg0(void)
{
	asm(
		"rev x0, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg1(void)
{
	asm(
		"rev x0, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg2(void)
{
	asm(
		"rev x0, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg3(void)
{
	asm(
		"rev x0, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg4(void)
{
	asm(
		"rev x0, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg5(void)
{
	asm(
		"rev x0, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg6(void)
{
	asm(
		"rev x0, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg7(void)
{
	asm(
		"rev x0, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg8(void)
{
	asm(
		"rev x0, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg9(void)
{
	asm(
		"rev x0, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg10(void)
{
	asm(
		"rev x0, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg11(void)
{
	asm(
		"rev x0, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg12(void)
{
	asm(
		"rev x0, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg13(void)
{
	asm(
		"rev x0, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg14(void)
{
	asm(
		"rev x0, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg0_arg15(void)
{
	asm(
		"rev x0, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg0(void)
{
	asm(
		"rev x1, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg1(void)
{
	asm(
		"rev x1, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg2(void)
{
	asm(
		"rev x1, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg3(void)
{
	asm(
		"rev x1, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg4(void)
{
	asm(
		"rev x1, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg5(void)
{
	asm(
		"rev x1, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg6(void)
{
	asm(
		"rev x1, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg7(void)
{
	asm(
		"rev x1, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg8(void)
{
	asm(
		"rev x1, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg9(void)
{
	asm(
		"rev x1, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg10(void)
{
	asm(
		"rev x1, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg11(void)
{
	asm(
		"rev x1, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg12(void)
{
	asm(
		"rev x1, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg13(void)
{
	asm(
		"rev x1, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg14(void)
{
	asm(
		"rev x1, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg1_arg15(void)
{
	asm(
		"rev x1, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg0(void)
{
	asm(
		"rev x2, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg1(void)
{
	asm(
		"rev x2, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg2(void)
{
	asm(
		"rev x2, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg3(void)
{
	asm(
		"rev x2, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg4(void)
{
	asm(
		"rev x2, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg5(void)
{
	asm(
		"rev x2, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg6(void)
{
	asm(
		"rev x2, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg7(void)
{
	asm(
		"rev x2, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg8(void)
{
	asm(
		"rev x2, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg9(void)
{
	asm(
		"rev x2, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg10(void)
{
	asm(
		"rev x2, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg11(void)
{
	asm(
		"rev x2, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg12(void)
{
	asm(
		"rev x2, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg13(void)
{
	asm(
		"rev x2, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg14(void)
{
	asm(
		"rev x2, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg2_arg15(void)
{
	asm(
		"rev x2, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg0(void)
{
	asm(
		"rev x3, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg1(void)
{
	asm(
		"rev x3, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg2(void)
{
	asm(
		"rev x3, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg3(void)
{
	asm(
		"rev x3, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg4(void)
{
	asm(
		"rev x3, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg5(void)
{
	asm(
		"rev x3, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg6(void)
{
	asm(
		"rev x3, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg7(void)
{
	asm(
		"rev x3, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg8(void)
{
	asm(
		"rev x3, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg9(void)
{
	asm(
		"rev x3, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg10(void)
{
	asm(
		"rev x3, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg11(void)
{
	asm(
		"rev x3, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg12(void)
{
	asm(
		"rev x3, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg13(void)
{
	asm(
		"rev x3, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg14(void)
{
	asm(
		"rev x3, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg3_arg15(void)
{
	asm(
		"rev x3, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg0(void)
{
	asm(
		"rev x4, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg1(void)
{
	asm(
		"rev x4, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg2(void)
{
	asm(
		"rev x4, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg3(void)
{
	asm(
		"rev x4, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg4(void)
{
	asm(
		"rev x4, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg5(void)
{
	asm(
		"rev x4, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg6(void)
{
	asm(
		"rev x4, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg7(void)
{
	asm(
		"rev x4, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg8(void)
{
	asm(
		"rev x4, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg9(void)
{
	asm(
		"rev x4, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg10(void)
{
	asm(
		"rev x4, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg11(void)
{
	asm(
		"rev x4, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg12(void)
{
	asm(
		"rev x4, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg13(void)
{
	asm(
		"rev x4, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg14(void)
{
	asm(
		"rev x4, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg4_arg15(void)
{
	asm(
		"rev x4, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg0(void)
{
	asm(
		"rev x5, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg1(void)
{
	asm(
		"rev x5, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg2(void)
{
	asm(
		"rev x5, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg3(void)
{
	asm(
		"rev x5, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg4(void)
{
	asm(
		"rev x5, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg5(void)
{
	asm(
		"rev x5, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg6(void)
{
	asm(
		"rev x5, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg7(void)
{
	asm(
		"rev x5, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg8(void)
{
	asm(
		"rev x5, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg9(void)
{
	asm(
		"rev x5, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg10(void)
{
	asm(
		"rev x5, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg11(void)
{
	asm(
		"rev x5, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg12(void)
{
	asm(
		"rev x5, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg13(void)
{
	asm(
		"rev x5, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg14(void)
{
	asm(
		"rev x5, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg5_arg15(void)
{
	asm(
		"rev x5, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg0(void)
{
	asm(
		"rev x6, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg1(void)
{
	asm(
		"rev x6, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg2(void)
{
	asm(
		"rev x6, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg3(void)
{
	asm(
		"rev x6, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg4(void)
{
	asm(
		"rev x6, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg5(void)
{
	asm(
		"rev x6, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg6(void)
{
	asm(
		"rev x6, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg7(void)
{
	asm(
		"rev x6, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg8(void)
{
	asm(
		"rev x6, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg9(void)
{
	asm(
		"rev x6, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg10(void)
{
	asm(
		"rev x6, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg11(void)
{
	asm(
		"rev x6, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg12(void)
{
	asm(
		"rev x6, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg13(void)
{
	asm(
		"rev x6, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg14(void)
{
	asm(
		"rev x6, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg6_arg15(void)
{
	asm(
		"rev x6, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg0(void)
{
	asm(
		"rev x7, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg1(void)
{
	asm(
		"rev x7, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg2(void)
{
	asm(
		"rev x7, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg3(void)
{
	asm(
		"rev x7, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg4(void)
{
	asm(
		"rev x7, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg5(void)
{
	asm(
		"rev x7, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg6(void)
{
	asm(
		"rev x7, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg7(void)
{
	asm(
		"rev x7, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg8(void)
{
	asm(
		"rev x7, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg9(void)
{
	asm(
		"rev x7, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg10(void)
{
	asm(
		"rev x7, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg11(void)
{
	asm(
		"rev x7, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg12(void)
{
	asm(
		"rev x7, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg13(void)
{
	asm(
		"rev x7, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg14(void)
{
	asm(
		"rev x7, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg7_arg15(void)
{
	asm(
		"rev x7, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg0(void)
{
	asm(
		"rev x8, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg1(void)
{
	asm(
		"rev x8, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg2(void)
{
	asm(
		"rev x8, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg3(void)
{
	asm(
		"rev x8, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg4(void)
{
	asm(
		"rev x8, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg5(void)
{
	asm(
		"rev x8, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg6(void)
{
	asm(
		"rev x8, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg7(void)
{
	asm(
		"rev x8, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg8(void)
{
	asm(
		"rev x8, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg9(void)
{
	asm(
		"rev x8, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg10(void)
{
	asm(
		"rev x8, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg11(void)
{
	asm(
		"rev x8, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg12(void)
{
	asm(
		"rev x8, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg13(void)
{
	asm(
		"rev x8, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg14(void)
{
	asm(
		"rev x8, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg8_arg15(void)
{
	asm(
		"rev x8, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg0(void)
{
	asm(
		"rev x9, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg1(void)
{
	asm(
		"rev x9, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg2(void)
{
	asm(
		"rev x9, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg3(void)
{
	asm(
		"rev x9, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg4(void)
{
	asm(
		"rev x9, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg5(void)
{
	asm(
		"rev x9, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg6(void)
{
	asm(
		"rev x9, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg7(void)
{
	asm(
		"rev x9, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg8(void)
{
	asm(
		"rev x9, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg9(void)
{
	asm(
		"rev x9, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg10(void)
{
	asm(
		"rev x9, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg11(void)
{
	asm(
		"rev x9, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg12(void)
{
	asm(
		"rev x9, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg13(void)
{
	asm(
		"rev x9, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg14(void)
{
	asm(
		"rev x9, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg9_arg15(void)
{
	asm(
		"rev x9, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg0(void)
{
	asm(
		"rev x10, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg1(void)
{
	asm(
		"rev x10, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg2(void)
{
	asm(
		"rev x10, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg3(void)
{
	asm(
		"rev x10, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg4(void)
{
	asm(
		"rev x10, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg5(void)
{
	asm(
		"rev x10, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg6(void)
{
	asm(
		"rev x10, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg7(void)
{
	asm(
		"rev x10, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg8(void)
{
	asm(
		"rev x10, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg9(void)
{
	asm(
		"rev x10, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg10(void)
{
	asm(
		"rev x10, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg11(void)
{
	asm(
		"rev x10, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg12(void)
{
	asm(
		"rev x10, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg13(void)
{
	asm(
		"rev x10, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg14(void)
{
	asm(
		"rev x10, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg10_arg15(void)
{
	asm(
		"rev x10, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg0(void)
{
	asm(
		"rev x11, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg1(void)
{
	asm(
		"rev x11, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg2(void)
{
	asm(
		"rev x11, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg3(void)
{
	asm(
		"rev x11, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg4(void)
{
	asm(
		"rev x11, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg5(void)
{
	asm(
		"rev x11, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg6(void)
{
	asm(
		"rev x11, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg7(void)
{
	asm(
		"rev x11, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg8(void)
{
	asm(
		"rev x11, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg9(void)
{
	asm(
		"rev x11, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg10(void)
{
	asm(
		"rev x11, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg11(void)
{
	asm(
		"rev x11, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg12(void)
{
	asm(
		"rev x11, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg13(void)
{
	asm(
		"rev x11, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg14(void)
{
	asm(
		"rev x11, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg11_arg15(void)
{
	asm(
		"rev x11, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg0(void)
{
	asm(
		"rev x12, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg1(void)
{
	asm(
		"rev x12, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg2(void)
{
	asm(
		"rev x12, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg3(void)
{
	asm(
		"rev x12, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg4(void)
{
	asm(
		"rev x12, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg5(void)
{
	asm(
		"rev x12, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg6(void)
{
	asm(
		"rev x12, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg7(void)
{
	asm(
		"rev x12, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg8(void)
{
	asm(
		"rev x12, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg9(void)
{
	asm(
		"rev x12, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg10(void)
{
	asm(
		"rev x12, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg11(void)
{
	asm(
		"rev x12, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg12(void)
{
	asm(
		"rev x12, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg13(void)
{
	asm(
		"rev x12, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg14(void)
{
	asm(
		"rev x12, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg12_arg15(void)
{
	asm(
		"rev x12, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg0(void)
{
	asm(
		"rev x13, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg1(void)
{
	asm(
		"rev x13, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg2(void)
{
	asm(
		"rev x13, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg3(void)
{
	asm(
		"rev x13, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg4(void)
{
	asm(
		"rev x13, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg5(void)
{
	asm(
		"rev x13, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg6(void)
{
	asm(
		"rev x13, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg7(void)
{
	asm(
		"rev x13, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg8(void)
{
	asm(
		"rev x13, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg9(void)
{
	asm(
		"rev x13, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg10(void)
{
	asm(
		"rev x13, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg11(void)
{
	asm(
		"rev x13, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg12(void)
{
	asm(
		"rev x13, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg13(void)
{
	asm(
		"rev x13, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg14(void)
{
	asm(
		"rev x13, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg13_arg15(void)
{
	asm(
		"rev x13, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg0(void)
{
	asm(
		"rev x14, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg1(void)
{
	asm(
		"rev x14, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg2(void)
{
	asm(
		"rev x14, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg3(void)
{
	asm(
		"rev x14, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg4(void)
{
	asm(
		"rev x14, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg5(void)
{
	asm(
		"rev x14, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg6(void)
{
	asm(
		"rev x14, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg7(void)
{
	asm(
		"rev x14, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg8(void)
{
	asm(
		"rev x14, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg9(void)
{
	asm(
		"rev x14, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg10(void)
{
	asm(
		"rev x14, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg11(void)
{
	asm(
		"rev x14, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg12(void)
{
	asm(
		"rev x14, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg13(void)
{
	asm(
		"rev x14, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg14(void)
{
	asm(
		"rev x14, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg14_arg15(void)
{
	asm(
		"rev x14, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg0(void)
{
	asm(
		"rev x15, x0 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg1(void)
{
	asm(
		"rev x15, x1 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg2(void)
{
	asm(
		"rev x15, x2 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg3(void)
{
	asm(
		"rev x15, x3 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg4(void)
{
	asm(
		"rev x15, x4 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg5(void)
{
	asm(
		"rev x15, x5 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg6(void)
{
	asm(
		"rev x15, x6 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg7(void)
{
	asm(
		"rev x15, x7 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg8(void)
{
	asm(
		"rev x15, x8 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg9(void)
{
	asm(
		"rev x15, x9 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg10(void)
{
	asm(
		"rev x15, x10 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg11(void)
{
	asm(
		"rev x15, x11 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg12(void)
{
	asm(
		"rev x15, x12 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg13(void)
{
	asm(
		"rev x15, x13 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg14(void)
{
	asm(
		"rev x15, x14 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

static __attribute__((naked)) void gadget_bswap64_arg15_arg15(void)
{
	asm(
		"rev x15, x15 \n"
		"ldr x27, [x28], #8 \n"
		"br x27 \n"
	);
}

const void* gadget_bswap64[16][16] = {
		{gadget_bswap64_arg0_arg0, gadget_bswap64_arg0_arg1, gadget_bswap64_arg0_arg2, gadget_bswap64_arg0_arg3, gadget_bswap64_arg0_arg4, gadget_bswap64_arg0_arg5, gadget_bswap64_arg0_arg6, gadget_bswap64_arg0_arg7, gadget_bswap64_arg0_arg8, gadget_bswap64_arg0_arg9, gadget_bswap64_arg0_arg10, gadget_bswap64_arg0_arg11, gadget_bswap64_arg0_arg12, gadget_bswap64_arg0_arg13, gadget_bswap64_arg0_arg14, gadget_bswap64_arg0_arg15, },
		{gadget_bswap64_arg1_arg0, gadget_bswap64_arg1_arg1, gadget_bswap64_arg1_arg2, gadget_bswap64_arg1_arg3, gadget_bswap64_arg1_arg4, gadget_bswap64_arg1_arg5, gadget_bswap64_arg1_arg6, gadget_bswap64_arg1_arg7, gadget_bswap64_arg1_arg8, gadget_bswap64_arg1_arg9, gadget_bswap64_arg1_arg10, gadget_bswap64_arg1_arg11, gadget_bswap64_arg1_arg12, gadget_bswap64_arg1_arg13, gadget_bswap64_arg1_arg14, gadget_bswap64_arg1_arg15, },
		{gadget_bswap64_arg2_arg0, gadget_bswap64_arg2_arg1, gadget_bswap64_arg2_arg2, gadget_bswap64_arg2_arg3, gadget_bswap64_arg2_arg4, gadget_bswap64_arg2_arg5, gadget_bswap64_arg2_arg6, gadget_bswap64_arg2_arg7, gadget_bswap64_arg2_arg8, gadget_bswap64_arg2_arg9, gadget_bswap64_arg2_arg10, gadget_bswap64_arg2_arg11, gadget_bswap64_arg2_arg12, gadget_bswap64_arg2_arg13, gadget_bswap64_arg2_arg14, gadget_bswap64_arg2_arg15, },
		{gadget_bswap64_arg3_arg0, gadget_bswap64_arg3_arg1, gadget_bswap64_arg3_arg2, gadget_bswap64_arg3_arg3, gadget_bswap64_arg3_arg4, gadget_bswap64_arg3_arg5, gadget_bswap64_arg3_arg6, gadget_bswap64_arg3_arg7, gadget_bswap64_arg3_arg8, gadget_bswap64_arg3_arg9, gadget_bswap64_arg3_arg10, gadget_bswap64_arg3_arg11, gadget_bswap64_arg3_arg12, gadget_bswap64_arg3_arg13, gadget_bswap64_arg3_arg14, gadget_bswap64_arg3_arg15, },
		{gadget_bswap64_arg4_arg0, gadget_bswap64_arg4_arg1, gadget_bswap64_arg4_arg2, gadget_bswap64_arg4_arg3, gadget_bswap64_arg4_arg4, gadget_bswap64_arg4_arg5, gadget_bswap64_arg4_arg6, gadget_bswap64_arg4_arg7, gadget_bswap64_arg4_arg8, gadget_bswap64_arg4_arg9, gadget_bswap64_arg4_arg10, gadget_bswap64_arg4_arg11, gadget_bswap64_arg4_arg12, gadget_bswap64_arg4_arg13, gadget_bswap64_arg4_arg14, gadget_bswap64_arg4_arg15, },
		{gadget_bswap64_arg5_arg0, gadget_bswap64_arg5_arg1, gadget_bswap64_arg5_arg2, gadget_bswap64_arg5_arg3, gadget_bswap64_arg5_arg4, gadget_bswap64_arg5_arg5, gadget_bswap64_arg5_arg6, gadget_bswap64_arg5_arg7, gadget_bswap64_arg5_arg8, gadget_bswap64_arg5_arg9, gadget_bswap64_arg5_arg10, gadget_bswap64_arg5_arg11, gadget_bswap64_arg5_arg12, gadget_bswap64_arg5_arg13, gadget_bswap64_arg5_arg14, gadget_bswap64_arg5_arg15, },
		{gadget_bswap64_arg6_arg0, gadget_bswap64_arg6_arg1, gadget_bswap64_arg6_arg2, gadget_bswap64_arg6_arg3, gadget_bswap64_arg6_arg4, gadget_bswap64_arg6_arg5, gadget_bswap64_arg6_arg6, gadget_bswap64_arg6_arg7, gadget_bswap64_arg6_arg8, gadget_bswap64_arg6_arg9, gadget_bswap64_arg6_arg10, gadget_bswap64_arg6_arg11, gadget_bswap64_arg6_arg12, gadget_bswap64_arg6_arg13, gadget_bswap64_arg6_arg14, gadget_bswap64_arg6_arg15, },
		{gadget_bswap64_arg7_arg0, gadget_bswap64_arg7_arg1, gadget_bswap64_arg7_arg2, gadget_bswap64_arg7_arg3, gadget_bswap64_arg7_arg4, gadget_bswap64_arg7_arg5, gadget_bswap64_arg7_arg6, gadget_bswap64_arg7_arg7, gadget_bswap64_arg7_arg8, gadget_bswap64_arg7_arg9, gadget_bswap64_arg7_arg10, gadget_bswap64_arg7_arg11, gadget_bswap64_arg7_arg12, gadget_bswap64_arg7_arg13, gadget_bswap64_arg7_arg14, gadget_bswap64_arg7_arg15, },
		{gadget_bswap64_arg8_arg0, gadget_bswap64_arg8_arg1, gadget_bswap64_arg8_arg2, gadget_bswap64_arg8_arg3, gadget_bswap64_arg8_arg4, gadget_bswap64_arg8_arg5, gadget_bswap64_arg8_arg6, gadget_bswap64_arg8_arg7, gadget_bswap64_arg8_arg8, gadget_bswap64_arg8_arg9, gadget_bswap64_arg8_arg10, gadget_bswap64_arg8_arg11, gadget_bswap64_arg8_arg12, gadget_bswap64_arg8_arg13, gadget_bswap64_arg8_arg14, gadget_bswap64_arg8_arg15, },
		{gadget_bswap64_arg9_arg0, gadget_bswap64_arg9_arg1, gadget_bswap64_arg9_arg2, gadget_bswap64_arg9_arg3, gadget_bswap64_arg9_arg4, gadget_bswap64_arg9_arg5, gadget_bswap64_arg9_arg6, gadget_bswap64_arg9_arg7, gadget_bswap64_arg9_arg8, gadget_bswap64_arg9_arg9, gadget_bswap64_arg9_arg10, gadget_bswap64_arg9_arg11, gadget_bswap64_arg9_arg12, gadget_bswap64_arg9_arg13, gadget_bswap64_arg9_arg14, gadget_bswap64_arg9_arg15, },
		{gadget_bswap64_arg10_arg0, gadget_bswap64_arg10_arg1, gadget_bswap64_arg10_arg2, gadget_bswap64_arg10_arg3, gadget_bswap64_arg10_arg4, gadget_bswap64_arg10_arg5, gadget_bswap64_arg10_arg6, gadget_bswap64_arg10_arg7, gadget_bswap64_arg10_arg8, gadget_bswap64_arg10_arg9, gadget_bswap64_arg10_arg10, gadget_bswap64_arg10_arg11, gadget_bswap64_arg10_arg12, gadget_bswap64_arg10_arg13, gadget_bswap64_arg10_arg14, gadget_bswap64_arg10_arg15, },
		{gadget_bswap64_arg11_arg0, gadget_bswap64_arg11_arg1, gadget_bswap64_arg11_arg2, gadget_bswap64_arg11_arg3, gadget_bswap64_arg11_arg4, gadget_bswap64_arg11_arg5, gadget_bswap64_arg11_arg6, gadget_bswap64_arg11_arg7, gadget_bswap64_arg11_arg8, gadget_bswap64_arg11_arg9, gadget_bswap64_arg11_arg10, gadget_bswap64_arg11_arg11, gadget_bswap64_arg11_arg12, gadget_bswap64_arg11_arg13, gadget_bswap64_arg11_arg14, gadget_bswap64_arg11_arg15, },
		{gadget_bswap64_arg12_arg0, gadget_bswap64_arg12_arg1, gadget_bswap64_arg12_arg2, gadget_bswap64_arg12_arg3, gadget_bswap64_arg12_arg4, gadget_bswap64_arg12_arg5, gadget_bswap64_arg12_arg6, gadget_bswap64_arg12_arg7, gadget_bswap64_arg12_arg8, gadget_bswap64_arg12_arg9, gadget_bswap64_arg12_arg10, gadget_bswap64_arg12_arg11, gadget_bswap64_arg12_arg12, gadget_bswap64_arg12_arg13, gadget_bswap64_arg12_arg14, gadget_bswap64_arg12_arg15, },
		{gadget_bswap64_arg13_arg0, gadget_bswap64_arg13_arg1, gadget_bswap64_arg13_arg2, gadget_bswap64_arg13_arg3, gadget_bswap64_arg13_arg4, gadget_bswap64_arg13_arg5, gadget_bswap64_arg13_arg6, gadget_bswap64_arg13_arg7, gadget_bswap64_arg13_arg8, gadget_bswap64_arg13_arg9, gadget_bswap64_arg13_arg10, gadget_bswap64_arg13_arg11, gadget_bswap64_arg13_arg12, gadget_bswap64_arg13_arg13, gadget_bswap64_arg13_arg14, gadget_bswap64_arg13_arg15, },
		{gadget_bswap64_arg14_arg0, gadget_bswap64_arg14_arg1, gadget_bswap64_arg14_arg2, gadget_bswap64_arg14_arg3, gadget_bswap64_arg14_arg4, gadget_bswap64_arg14_arg5, gadget_bswap64_arg14_arg6, gadget_bswap64_arg14_arg7, gadget_bswap64_arg14_arg8, gadget_bswap64_arg14_arg9, gadget_bswap64_arg14_arg10, gadget_bswap64_arg14_arg11, gadget_bswap64_arg14_arg12, gadget_bswap64_arg14_arg13, gadget_bswap64_arg14_arg14, gadget_bswap64_arg14_arg15, },
		{gadget_bswap64_arg15_arg0, gadget_bswap64_arg15_arg1, gadget_bswap64_arg15_arg2, gadget_bswap64_arg15_arg3, gadget_bswap64_arg15_arg4, gadget_bswap64_arg15_arg5, gadget_bswap64_arg15_arg6, gadget_bswap64_arg15_arg7, gadget_bswap64_arg15_arg8, gadget_bswap64_arg15_arg9, gadget_bswap64_arg15_arg10, gadget_bswap64_arg15_arg11, gadget_bswap64_arg15_arg12, gadget_bswap64_arg15_arg13, gadget_bswap64_arg15_arg14, gadget_bswap64_arg15_arg15, },
};
