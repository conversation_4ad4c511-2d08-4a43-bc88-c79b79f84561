/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_qemu_ld_sb_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_ld_sb_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_ld_lesw_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_ld_lesw_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_ld_lesl_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_ld_lesl_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_ld_ub_slowpath_mode02_off0_i32[16][16];
extern const void* gadget_qemu_ld_ub_slowpath_mode02_off0_i64[16][16];
extern const void* gadget_qemu_ld_leq_slowpath_mode32_off0_i32[16][16];
extern const void* gadget_qemu_ld_leq_slowpath_mode32_off0_i64[16][16];
extern const void* gadget_qemu_ld_leq_slowpath_mode3a_off0_i32[16][16];
extern const void* gadget_qemu_ld_leq_slowpath_mode3a_off0_i64[16][16];
