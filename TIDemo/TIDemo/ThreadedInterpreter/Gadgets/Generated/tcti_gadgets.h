/* Automatically generated by tcti-gadget-gen.py. Do not edit. */

#include "tcti_misc_gadgets.h"
#include "tcti_setcond_gadgets.h"
#include "tcti_brcond_gadgets.h"
#include "tcti_mov_gadgets.h"
#include "tcti_load_unsigned_gadgets.h"
#include "tcti_load_signed_gadgets.h"
#include "tcti_store_gadgets.h"
#include "tcti_arithmetic_gadgets.h"
#include "tcti_logical_gadgets.h"
#include "tcti_bitwise_gadgets.h"
#include "tcti_extension_gadgets.h"
#include "tcti_byteswap_gadgets.h"
#include "tcti_qemu_ld_aligned_unsigned_le_gadgets.h"
#include "tcti_qemu_ld_aligned_signed_le_gadgets.h"
#include "tcti_qemu_ld_unaligned_unsigned_le_gadgets.h"
#include "tcti_qemu_ld_unaligned_signed_le_gadgets.h"
#include "tcti_qemu_ld_slowpath_unsigned_le_gadgets.h"
#include "tcti_qemu_ld_slowpath_signed_le_gadgets.h"
#include "tcti_qemu_st_aligned_le_gadgets.h"
#include "tcti_qemu_st_unaligned_le_gadgets.h"
#include "tcti_qemu_st_slowpath_le_gadgets.h"
#include "tcti_simd_base_gadgets.h"
#include "tcti_simd_arithmetic_gadgets.h"
#include "tcti_simd_logical_gadgets.h"
#include "tcti_simd_immediate_gadgets.h"
