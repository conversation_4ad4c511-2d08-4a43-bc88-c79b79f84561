/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_qemu_st_ub_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_st_ub_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_st_leuw_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_st_leuw_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_st_leul_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_st_leul_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_st_leq_slowpath_off0_i32[16][16];
extern const void* gadget_qemu_st_leq_slowpath_off0_i64[16][16];
extern const void* gadget_qemu_st_ub_slowpath_mode02_off0_i32[16][16];
extern const void* gadget_qemu_st_ub_slowpath_mode02_off0_i64[16][16];
extern const void* gadget_qemu_st_leq_slowpath_mode32_off0_i32[16][16];
extern const void* gadget_qemu_st_leq_slowpath_mode32_off0_i64[16][16];
extern const void* gadget_qemu_st_leq_slowpath_mode3a_off0_i32[16][16];
extern const void* gadget_qemu_st_leq_slowpath_mode3a_off0_i64[16][16];
