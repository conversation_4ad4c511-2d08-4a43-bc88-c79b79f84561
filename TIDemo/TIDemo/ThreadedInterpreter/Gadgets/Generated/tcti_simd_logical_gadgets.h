/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_and_d[16][16][16];
extern const void* gadget_and_q[16][16][16];
extern const void* gadget_andc_d[16][16][16];
extern const void* gadget_andc_q[16][16][16];
extern const void* gadget_or_d[16][16][16];
extern const void* gadget_or_q[16][16][16];
extern const void* gadget_orc_d[16][16][16];
extern const void* gadget_orc_q[16][16][16];
extern const void* gadget_xor_d[16][16][16];
extern const void* gadget_xor_q[16][16][16];
extern const void* gadget_not_d[16][16];
extern const void* gadget_not_q[16][16];
extern const void* gadget_neg_16b[16][16];
extern const void* gadget_neg_8b[16][16];
extern const void* gadget_neg_4h[16][16];
extern const void* gadget_neg_8h[16][16];
extern const void* gadget_neg_2s[16][16];
extern const void* gadget_neg_4s[16][16];
extern const void* gadget_neg_2d[16][16];
extern const void* gadget_abs_16b[16][16];
extern const void* gadget_abs_8b[16][16];
extern const void* gadget_abs_4h[16][16];
extern const void* gadget_abs_8h[16][16];
extern const void* gadget_abs_2s[16][16];
extern const void* gadget_abs_4s[16][16];
extern const void* gadget_abs_2d[16][16];
extern const void* gadget_bit_d[16][16][16];
extern const void* gadget_bit_q[16][16][16];
extern const void* gadget_bif_d[16][16][16];
extern const void* gadget_bif_q[16][16][16];
extern const void* gadget_bsl_d[16][16][16];
extern const void* gadget_bsl_q[16][16][16];
extern const void* gadget_shlv_16b[16][16][16];
extern const void* gadget_shlv_8b[16][16][16];
extern const void* gadget_shlv_4h[16][16][16];
extern const void* gadget_shlv_8h[16][16][16];
extern const void* gadget_shlv_2s[16][16][16];
extern const void* gadget_shlv_4s[16][16][16];
extern const void* gadget_shlv_2d[16][16][16];
extern const void* gadget_shlv_scalar[16][16][16];
extern const void* gadget_sshl_16b[16][16][16];
extern const void* gadget_sshl_8b[16][16][16];
extern const void* gadget_sshl_4h[16][16][16];
extern const void* gadget_sshl_8h[16][16][16];
extern const void* gadget_sshl_2s[16][16][16];
extern const void* gadget_sshl_4s[16][16][16];
extern const void* gadget_sshl_2d[16][16][16];
extern const void* gadget_sshl_scalar[16][16][16];
extern const void* gadget_cmeq_16b[16][16][16];
extern const void* gadget_cmeq_8b[16][16][16];
extern const void* gadget_cmeq_4h[16][16][16];
extern const void* gadget_cmeq_8h[16][16][16];
extern const void* gadget_cmeq_2s[16][16][16];
extern const void* gadget_cmeq_4s[16][16][16];
extern const void* gadget_cmeq_2d[16][16][16];
extern const void* gadget_cmeq_scalar[16][16][16];
extern const void* gadget_cmgt_16b[16][16][16];
extern const void* gadget_cmgt_8b[16][16][16];
extern const void* gadget_cmgt_4h[16][16][16];
extern const void* gadget_cmgt_8h[16][16][16];
extern const void* gadget_cmgt_2s[16][16][16];
extern const void* gadget_cmgt_4s[16][16][16];
extern const void* gadget_cmgt_2d[16][16][16];
extern const void* gadget_cmgt_scalar[16][16][16];
extern const void* gadget_cmge_16b[16][16][16];
extern const void* gadget_cmge_8b[16][16][16];
extern const void* gadget_cmge_4h[16][16][16];
extern const void* gadget_cmge_8h[16][16][16];
extern const void* gadget_cmge_2s[16][16][16];
extern const void* gadget_cmge_4s[16][16][16];
extern const void* gadget_cmge_2d[16][16][16];
extern const void* gadget_cmge_scalar[16][16][16];
extern const void* gadget_cmhi_16b[16][16][16];
extern const void* gadget_cmhi_8b[16][16][16];
extern const void* gadget_cmhi_4h[16][16][16];
extern const void* gadget_cmhi_8h[16][16][16];
extern const void* gadget_cmhi_2s[16][16][16];
extern const void* gadget_cmhi_4s[16][16][16];
extern const void* gadget_cmhi_2d[16][16][16];
extern const void* gadget_cmhi_scalar[16][16][16];
extern const void* gadget_cmhs_16b[16][16][16];
extern const void* gadget_cmhs_8b[16][16][16];
extern const void* gadget_cmhs_4h[16][16][16];
extern const void* gadget_cmhs_8h[16][16][16];
extern const void* gadget_cmhs_2s[16][16][16];
extern const void* gadget_cmhs_4s[16][16][16];
extern const void* gadget_cmhs_2d[16][16][16];
extern const void* gadget_cmhs_scalar[16][16][16];
