/* Automatically generated by tcti-gadget-gen.py. Do not edit. */


#pragma once

extern const void* gadget_brcond_i32_eq[16][16][16];
extern const void* gadget_brcond_i64_eq[16][16][16];
extern const void* gadget_setcond_i32_ne[16][16][16];
extern const void* gadget_setcond_i64_ne[16][16][16];
extern const void* gadget_brcond_i32_ne[16][16][16];
extern const void* gadget_brcond_i64_ne[16][16][16];
extern const void* gadget_setcond_i32_lt[16][16][16];
extern const void* gadget_setcond_i64_lt[16][16][16];
extern const void* gadget_brcond_i32_lt[16][16][16];
extern const void* gadget_brcond_i64_lt[16][16][16];
extern const void* gadget_setcond_i32_ge[16][16][16];
extern const void* gadget_setcond_i64_ge[16][16][16];
extern const void* gadget_brcond_i32_ge[16][16][16];
extern const void* gadget_brcond_i64_ge[16][16][16];
extern const void* gadget_setcond_i32_le[16][16][16];
extern const void* gadget_setcond_i64_le[16][16][16];
extern const void* gadget_brcond_i32_le[16][16][16];
extern const void* gadget_brcond_i64_le[16][16][16];
extern const void* gadget_setcond_i32_gt[16][16][16];
extern const void* gadget_setcond_i64_gt[16][16][16];
extern const void* gadget_brcond_i32_gt[16][16][16];
extern const void* gadget_brcond_i64_gt[16][16][16];
extern const void* gadget_setcond_i32_lo[16][16][16];
extern const void* gadget_setcond_i64_lo[16][16][16];
extern const void* gadget_brcond_i32_lo[16][16][16];
extern const void* gadget_brcond_i64_lo[16][16][16];
extern const void* gadget_setcond_i32_hs[16][16][16];
extern const void* gadget_setcond_i64_hs[16][16][16];
extern const void* gadget_brcond_i32_hs[16][16][16];
extern const void* gadget_brcond_i64_hs[16][16][16];
extern const void* gadget_setcond_i32_ls[16][16][16];
extern const void* gadget_setcond_i64_ls[16][16][16];
extern const void* gadget_brcond_i32_ls[16][16][16];
extern const void* gadget_brcond_i64_ls[16][16][16];
extern const void* gadget_setcond_i32_hi[16][16][16];
extern const void* gadget_setcond_i64_hi[16][16][16];
extern const void* gadget_brcond_i32_hi[16][16][16];
extern const void* gadget_brcond_i64_hi[16][16][16];
