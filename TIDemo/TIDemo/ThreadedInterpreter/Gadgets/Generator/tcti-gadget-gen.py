#!/usr/bin/env python3
""" Gadget-code generator for QEMU TCTI on AArch64. 

Generates a C-code include file containing 'gadgets' for use by TCTI.
"""

import os
import sys
import itertools

# Epilogue code follows at the end of each gadget, and handles continuing execution.
EPILOGUE = ( 
    # Load our next gadget address from our bytecode stream, advancing it.
    "ldr x27, [x28], #8",

    # Jump to the next gadget.
    "br x27"
)

# The number of general-purpose registers we're affording the TCG. This must match
# the configuration in the TCTI target.
TCG_REGISTER_COUNT   = 16
TCG_REGISTER_NUMBERS = list(range(TCG_REGISTER_COUNT))

# Helper that provides each of the AArch64 condition codes of interest.
ARCH_CONDITION_CODES = ["eq", "ne", "lt", "ge", "le", "gt", "lo", "hs", "ls", "hi"]

# The list of vector size codes supported on this platform.
VECTOR_SIZES = ['16b', '8b', '4h', '8h', '2s', '4s', '2d']

# We'll create a variety of gadgets that assume the MMU's TLB is stored at certain
# offsets into its structure. These should match the offsets in tcg-target.c.in.
QEMU_ALLOWED_MMU_OFFSETS = [ 32, 48, 64, 96, 128 ]

# Statistics.
gadgets      = 0
instructions = 0

# Files to write to.
current_collection = "basic"
output_files = {}

# Create a top-level header.
top_header = open("../Generated/tcti_gadgets.h", "w")
print("/* Automatically generated by tcti-gadget-gen.py. Do not edit. */\n", file=top_header)

def _get_output_files():
    """ Gathers the output C and H files for a given gadget-cluster name. """

    # If we don't have an output file for this already, create it.
    return output_files[current_collection]


def START_COLLECTION(name):
    """ Sets the name of the current collection. """

    global current_collection

    # If we already have a collection for this, skip it.
    if name in output_files:
        return

    # Create the relevant output files
    new_c_file = open(f"../Generated/tcti_{name}_gadgets.c", "w")
    new_h_file = open(f"../Generated/tcti_{name}_gadgets.h", "w")
    output_files[name] = (new_c_file, new_h_file)

    # Add the file to our gadget collection.
    print(f'#include "tcti_{name}_gadgets.h"', file=top_header)

    # Add generated messages to the relevant collection.
    print("/* Automatically generated by tcti-gadget-gen.py. Do not edit. */\n", file=new_c_file)
    print("/* Automatically generated by tcti-gadget-gen.py. Do not edit. */\n", file=new_h_file)

    # Start our C file with inclusion of the relevant header.
    print(f'\n#include "tcti_{name}_gadgets.h"\n', file=new_c_file)

    # Start our H file with a simple pragma-guard, for speed.
    print('\n#pragma once\n', file=new_h_file)

    # Finally, set the global active collection.
    current_collection = name
    

def simple(name, *lines, export=True):
    """ Generates a simple gadget that needs no per-register specialization. """

    global gadgets, instructions

    gadgets += 1

    # Fetch the files we'll be using for output.
    c_file, h_file = _get_output_files()

    # Create our C/ASM framing.
    if export:
        print(f"__attribute__((naked)) void gadget_{name}(void);", file=h_file)
        print(f"__attribute__((naked)) void gadget_{name}(void)", file=c_file)
    else:
        print(f"static __attribute__((naked)) void gadget_{name}(void)", file=c_file)

    print("{", file=c_file)

    # Add the core gadget
    print("\tasm(", file=c_file)
    for line in lines + EPILOGUE:
        print(f"\t\t\"{line} \\n\"", file=c_file)
        instructions += 1
    print("\t);", file=c_file)

    # End our framing.
    print("}\n", file=c_file)



def with_register_substitutions(name, substitutions, *lines, immediate_range=range(0)):
    """ Generates a collection of gadgtes with register substitutions. """

    def _expand_op1_immediate(num):
        """ Gets a uncompressed bitfield argument for a given immediate; for NEON instructions. 
        
        Duplciates each bit eight times; converting 0b0100 to 0x00FF0000.
        """

        # Get the number as a binary string...
        binstring = bin(num)[2:]

        # ... expand out the values to hex...
        hex_string = binstring.replace('1', 'FF').replace('0', '00') 

        # ... and return out the new constant.
        return f"0x{hex_string}"


    def substitutions_for_letter(letter, number, line):
        """ Helper that transforms Wd => w1, implementing gadget substitutions. """

        # Register substitutions...
        line = line.replace(f"X{letter}", f"x{number}")
        line = line.replace(f"W{letter}", f"w{number}")

        # ... vector register substitutions...
        line = line.replace(f"V{letter}", f"v{number + 16}")
        line = line.replace(f"D{letter}", f"d{number + 16}")
        line = line.replace(f"Q{letter}", f"q{number + 16}")

        # ... regular immediate substitutions...
        line = line.replace(f"I{letter}", f"{number}")

        # ... and compressed immediate substitutions.
        line = line.replace(f"S{letter}", f"{_expand_op1_immediate(number)}")
        return line

        
    # Build a list of all the various stages we'll iterate over...
    immediate_parameters = list(immediate_range)
    parameters   = ([TCG_REGISTER_NUMBERS] * len(substitutions))

    # ... adding immediates, if need be.
    if immediate_parameters:
        parameters.append(immediate_parameters)
        substitutions = substitutions + ['i']

    # Generate a list of register-combinations we'll support.
    permutations = itertools.product(*parameters)

    #  For each permutation...
    for permutation in permutations:
        new_lines = lines

        # Replace each placeholder element with its proper value...
        for index, element in enumerate(permutation):
            letter = substitutions[index]
            number = element

            # Create new gadgets for the releavnt line...
            new_lines = [substitutions_for_letter(letter, number, line) for line in new_lines]

        # ... and emit the gadget.
        permutation_id = "_arg".join(str(number) for number in permutation)
        simple(f"{name}_arg{permutation_id}", *new_lines, export=False)


def with_dnm(name, *lines):
    """ Generates a collection of gadgets with substitutions for Xd, Xn, and Xm, and equivalents. """
    with_register_substitutions(name, ("d", "n", "m"), *lines)

    # Fetch the files we'll be using for output.
    c_file, h_file = _get_output_files()

    # Print out an extern.
    print(f"extern const void* gadget_{name}[{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}];", file=h_file)

    # Print out an array that contains all of our gadgets, for lookup.
    print(f"const void* gadget_{name}[{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}] = ", end="", file=c_file)
    print("{", file=c_file)

    # D array
    for d in TCG_REGISTER_NUMBERS:
        print("\t{", file=c_file)

        # N array
        for n in TCG_REGISTER_NUMBERS:
            print("\t\t{", end="", file=c_file)

            # M array
            for m in TCG_REGISTER_NUMBERS:
                print(f"gadget_{name}_arg{d}_arg{n}_arg{m}", end=", ", file=c_file)

            print("},", file=c_file)
        print("\t},", file=c_file)
    print("};", file=c_file)


def with_dn_immediate(name, *lines, immediate_range):
    """ Generates a collection of gadgets with substitutions for Xd, Xn, and Xm, and equivalents. """
    with_register_substitutions(name, ["d", "n"], *lines, immediate_range=immediate_range)

    # Fetch the files we'll be using for output.
    c_file, h_file = _get_output_files()

    # Print out an extern.
    print(f"extern const void* gadget_{name}[{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}][{len(immediate_range)}];", file=h_file)

    # Print out an array that contains all of our gadgets, for lookup.
    print(f"const void* gadget_{name}[{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}][{len(immediate_range)}] = ", end="", file=c_file)
    print("{", file=c_file)

    # D array
    for d in TCG_REGISTER_NUMBERS:
        print("\t{", file=c_file)

        # N array
        for n in TCG_REGISTER_NUMBERS:
            print("\t\t{", end="", file=c_file)

            # M array
            for i in immediate_range:
                print(f"gadget_{name}_arg{d}_arg{n}_arg{i}", end=", ", file=c_file)

            print("},", file=c_file)
        print("\t},", file=c_file)
    print("};", file=c_file)


def with_pair(name, substitutions, *lines):
    """ Generates a collection of gadgets with two subtstitutions."""
    with_register_substitutions(name, substitutions, *lines)

    # Fetch the files we'll be using for output.
    c_file, h_file = _get_output_files()

    print(f"extern const void* gadget_{name}[{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}];", file=h_file)

    # Print out an array that contains all of our gadgets, for lookup.
    print(f"const void* gadget_{name}[{TCG_REGISTER_COUNT}][{TCG_REGISTER_COUNT}] = ", end="", file=c_file)
    print("{", file=c_file)

    # N array
    for a in TCG_REGISTER_NUMBERS:
        print("\t\t{", end="", file=c_file)

        # M array
        for b in TCG_REGISTER_NUMBERS:
            print(f"gadget_{name}_arg{a}_arg{b}", end=", ", file=c_file)

        print("},", file=c_file)
    print("};", file=c_file)


def math_dnm(name, mnemonic):
    """ Equivalent to `with_dnm`, but creates a _i32 and _i64 variant. For simple math. """
    with_dnm(f'{name}_i32', f"{mnemonic} Wd, Wn, Wm")
    with_dnm(f'{name}_i64', f"{mnemonic} Xd, Xn, Xm")

def math_dn(name, mnemonic, source_is_wn=False):
    """ Equivalent to `with_dn`, but creates a _i32 and _i64 variant. For simple math. """
    with_dn(f'{name}_i32', f"{mnemonic} Wd, Wn")
    with_dn(f'{name}_i64', f"{mnemonic} Xd, Wn" if source_is_wn else f"{mnemonic} Xd, Xn")


def with_nm(name, *lines):
    """ Generates a collection of gadgets with substitutions for Xn, and Xm, and equivalents. """
    with_pair(name, ('n', 'm',), *lines)


def with_dn(name, *lines):
    """ Generates a collection of gadgets with substitutions for Xd, and Xn, and equivalents. """
    with_pair(name, ('d', 'n',), *lines)


def ldst_dn(name, *lines):
    """ Generates a collection of gadgets with substitutions for Xd, and Xn, and equivalents. 
    
    This variant is optimized for loads and stores, and optimizes common offset cases.
    """

    #
    # Simple case: create our gadgets.
    #
    with_dn(name, "ldr x27, [x28], #8", *lines)

    #
    # Optimization case: create variants of our gadgets with our offsets replaced with common immediates.
    #
    immediate_lines_pos = [line.replace("x27", "#Ii") for line in lines]
    with_dn_immediate(f"{name}_imm", *immediate_lines_pos, immediate_range=range(64))

    immediate_lines_aligned = [line.replace("x27", "#(Ii << 3)") for line in lines]
    with_dn_immediate(f"{name}_sh8_imm", *immediate_lines_aligned, immediate_range=range(64))

    immediate_lines_neg = [line.replace("x27", "#-Ii") for line in lines]
    with_dn_immediate(f"{name}_neg_imm", *immediate_lines_neg, immediate_range=range(64))


def with_single(name, substitution, *lines):
    """ Generates a collection of gadgets with two subtstitutions."""
    with_register_substitutions(name, (substitution,), *lines)

    # Fetch the files we'll be using for output.
    c_file, h_file = _get_output_files()

    print(f"extern const void* gadget_{name}[{TCG_REGISTER_COUNT}];", file=h_file)

    # Print out an array that contains all of our gadgets, for lookup.
    print(f"const void* gadget_{name}[{TCG_REGISTER_COUNT}] = ", end="", file=c_file)
    print("{", file=c_file)

    for n in TCG_REGISTER_NUMBERS:
        print(f"gadget_{name}_arg{n}", end=", ", file=c_file)

    print("};", file=c_file)


def with_d_immediate(name, *lines, immediate_range=range(0)):
    """ Generates a collection of gadgets with two subtstitutions."""
    with_register_substitutions(name, ['d'], *lines, immediate_range=immediate_range)

    # Fetch the files we'll be using for output.
    c_file, h_file = _get_output_files()

    print(f"extern void* gadget_{name}[{TCG_REGISTER_COUNT}][{len(immediate_range)}];", file=h_file)

    # Print out an array that contains all of our gadgets, for lookup.
    print(f"void* gadget_{name}[{TCG_REGISTER_COUNT}][{len(immediate_range)}] = ", end="", file=c_file)
    print("{", file=c_file)

    # D array
    for a in TCG_REGISTER_NUMBERS:
        print("\t\t{", end="", file=c_file)

        # I array
        for b in immediate_range:
            print(f"gadget_{name}_arg{a}_arg{b}", end=", ", file=c_file)

        print("},", file=c_file)
    print("};", file=c_file)



def with_d(name, *lines):
    """ Generates a collection of gadgets with substitutions for Xd. """
    with_single(name, 'd', *lines)


# Assembly code for saving our machine state before entering the C runtime.
C_CALL_PROLOGUE = [
    "stp x14, x15, [sp, #-16]!",
    "stp x28, lr,  [sp, #-16]!",
]

# Assembly code for restoring our machine state after leaving the C runtime.
C_CALL_EPILOGUE = [
    "ldp x28, lr,  [sp], #16",
    "ldp x14, x15, [sp], #16",
]


def create_tlb_fastpath(is_aligned, is_write, offset, miss_label="0"):
    """ Creates a set of instructions that perform a soft-MMU TLB lookup.

    This is used for `qemu_ld`/qemu_st` instructions; to emit a prologue that
    hopefully helps us skip a slow call into the C runtime when a Guest Virtual 
    -> Host Virtual mapping is in the softmmu's TLB.

    This "fast-path" prelude behaves as follows:
        - If a TLB entry is found for the address stored in Xn, then x27
          is stored to an "addend" that can be added to the guest virtual addres
          to get the host virtual address (the address in our local memory space).
        - If a TLB entry isn't found, it branches to the "miss_label" (by default, 0:),
          so address lookup can be handled by the fastpath.

    Clobbers x24, and x26; provides output in x27.
    """

    fast_path = [
        # Load env_tlb(env)->f[mmu_idx].{mask,table} into {x26,x27}.
        f"ldp x26, x27, [x14, #-{offset}]",

        # Extract the TLB index from the address into X26. 
        "and x26, x26, Xn, lsr #7", # Xn = addr regsiter 

        # Add the tlb_table pointer, creating the CPUTLBEntry address into X27. 
        "add x27, x27, x26",

        # Load the tlb comparator into X26, and the fast path addend into X27. 
        "ldr x26, [x27, #8]" if is_write else "ldr x26, [x27]",
        "ldr x27, [x27, #0x18]",

    ]

    if is_aligned:
        fast_path.extend([
            # Store the page mask part of the address into X24.
            "and x24, Xn, #0xfffffffffffff000",

            # Compare the masked address with the TLB value.
            "cmp x26, x24",

            # If we're not equal, this isn't a TLB hit. Jump to our miss handler.
            f"b.ne {miss_label}f",
        ])
    else:
        fast_path.extend([
            # If we're not aligned, add in our alignment value to ensure we don't
            # don't straddle the end of a page.
            "add x24, Xn, #7",

            # Store the page mask part of the address into X24.
            "and x24, x24, #0xfffffffffffff000",

            # Compare the masked address with the TLB value.
            "cmp x26, x24",

            # If we're not equal, this isn't a TLB hit. Jump to our miss handler.
            f"b.ne {miss_label}f",
        ])

    return fast_path



def ld_thunk(name, fastpath_32b, fastpath_64b, slowpath_helper, immediate=None, is_aligned=False, force_slowpath=False):
    """ Creates a thunk into our C runtime for a QEMU ST operation. """

    # Use only offset 0 (no real offset) if we're forcing slowpath; 
    # otherwise, use all of our allowed MMU offsets.
    offsets = [0] if force_slowpath else QEMU_ALLOWED_MMU_OFFSETS
    for offset in offsets:
        for is_32b in (True, False):
            fastpath = fastpath_32b if is_32b else fastpath_64b

            gadget_name = f"{name}_off{offset}_i32" if is_32b else f"{name}_off{offset}_i64"
            postscript = () if immediate else ("add x28, x28, #8",)

            # If we have a pure-assembly fast path, start our gadget with it.
            if fastpath and not force_slowpath:
                fastpath_ops = [
                    # Create a fastpath that jumps to miss_lable on a TLB miss,
                    # or sets x27 to the TLB addend on a TLB hit.
                    *create_tlb_fastpath(is_aligned=is_aligned, is_write=False, offset=offset),

                    # On a hit, we can just perform an appropriate load...
                    *fastpath,

                    # Run our patch-up post-script, if we have one.
                    *postscript,

                    # ... and then we're done!
                    *EPILOGUE,
                ]
            # Otherwise, we'll save arguments for our slow path.
            else:
                fastpath_ops = []

            #
            # If we're not taking our fast path, we'll call into our C runtime to take the slow path.
            # 
            with_dn(gadget_name, 
                    *fastpath_ops,

                "0:",
                    "mov x27, Xn",

                    # Save our registers in preparation for entering a C call.
                    *C_CALL_PROLOGUE,

                    # Per our calling convention:
                    # - Move our architectural environment into x0, from x14.
                    # - Move our target address into x1. [Placed in x27 below.]
                    # - Move our operation info into x2, from an immediate32.
                    # - Move the next bytecode pointer into x3, from x28.
                    "mov   x0, x14",
                    "mov   x1, x27",
                    f"mov   x2, #{immediate}" if (immediate is not None) else "ldr   x2, [x28], #8", 
                    "mov   x3, x28",

                    # Perform our actual core code.
                    f"bl _{slowpath_helper}",

                    # Temporarily store our result in a register that won't get trashed.
                    "mov x27, x0",

                    # Restore our registers after our C call.
                    *C_CALL_EPILOGUE,

                    # Finally, call our postscript...
                    *postscript,

                    # ... and place our results in the target register.
                    "mov Wd, w27" if is_32b else "mov Xd, x27"
            )


def st_thunk(name, fastpath_32b, fastpath_64b, slowpath_helper, immediate=None, is_aligned=False, force_slowpath=False):
    """ Creates a thunk into our C runtime for a QEMU ST operation. """

    # Use only offset 0 (no real offset) if we're forcing slowpath; 
    # otherwise, use all of our allowed MMU offsets.
    offsets = [0] if force_slowpath else QEMU_ALLOWED_MMU_OFFSETS
    for offset in offsets:

        for is_32b in (True, False):
            fastpath = fastpath_32b if is_32b else fastpath_64b

            gadget_name = f"{name}_off{offset}_i32" if is_32b else f"{name}_off{offset}_i64"
            postscript = () if immediate else ("add x28, x28, #8",)

            # If we have a pure-assembly fast path, start our gadget with it.
            if fastpath and not force_slowpath:
                fastpath_ops = [

                    # Create a fastpath that jumps to miss_lable on a TLB miss,
                    # or sets x27 to the TLB addend on a TLB hit.
                    *create_tlb_fastpath(is_aligned=is_aligned, is_write=True, offset=offset),

                    # On a hit, we can just perform an appropriate load...
                    *fastpath,

                    # Run our patch-up post-script, if we have one.
                    *postscript,

                    # ... and then we're done!
                    *EPILOGUE,
                ]
            else:
                fastpath_ops = []


            #
            # If we're not taking our fast path, we'll call into our C runtime to take the slow path.
            # 
            with_dn(gadget_name, 
                    *fastpath_ops,

                "0:",
                    # Move our arguments into registers that we're not actively using.
                    # This ensures that they won't be trounced by our calling convention
                    # if this is reading values from x0-x4.
                    "mov w27, Wd" if is_32b else "mov x27, Xd",
                    "mov x26, Xn",

                    # Save our registers in preparation for entering a C call.
                    *C_CALL_PROLOGUE,

                    # Per our calling convention:
                    # - Move our architectural environment into x0, from x14.
                    # - Move our target address into x1. [Moved into x26 above].
                    # - Move our target value into x2. [Moved into x27 above].
                    # - Move our operation info into x3, from an immediate32.
                    # - Move the next bytecode pointer into x4, from x28.
                    "mov   x0, x14",
                    "mov   x1, x26",
                    "mov   x2, x27",
                    f"mov  x3, #{immediate}" if (immediate is not None) else "ldr   x3, [x28], #8", 
                    "mov   x4, x28",

                    # Perform our actual core code.
                    f"bl _{slowpath_helper}",

                    # Restore our registers after our C call.
                    *C_CALL_EPILOGUE,

                    # Finally, call our postscript.
                    *postscript
            )



def vector_dn(name, *lines):
    """ Creates a set of gadgets for every size of a given vector op. Accepts 'S' as a size placeholder. """

    def do_size_replacement(line, size):
        line = line.replace(".S", f".{size}")
        
        # If this size requires a 32b register, replace Wd with Xd.
        if size == "2d":
            line = line.replace("Wn", "Xn")

        return line


    # Create a variant for each size, replacing any placeholders.
    for size in VECTOR_SIZES:
        sized_lines = (do_size_replacement(line, size) for line in lines)
        with_dn(f"{name}_{size}", *sized_lines)


def vector_dnm(name, *lines, scalar=None, omit_sizes=()):
    """ Creates a set of gadgets for every size of a given vector op. Accepts 'S' as a size placeholder. """

    def do_size_replacement(line, size):
        return line.replace(".S", f".{size}")
        
    # Create a variant for each size, replacing any placeholders.
    for size in VECTOR_SIZES:
        if size in omit_sizes:
            continue

        sized_lines = (do_size_replacement(line, size) for line in lines)
        with_dnm(f"{name}_{size}", *sized_lines)

    if scalar:
        if isinstance(scalar, str):
            sized_lines = (scalar,)
        with_dnm(f"{name}_scalar", *sized_lines)


def vector_math_dnm(name, operation):
    """ Generates a collection of gadgets for vector math instructions. """
    vector_dnm(name, f"{operation} Vd.S, Vn.S, Vm.S", scalar=f"{operation} Dd, Dn, Dm")


def vector_math_dnm_no64(name, operation):
    """ Generates a collection of gadgets for vector math instructions. """
    vector_dnm(name, f"{operation} Vd.S, Vn.S, Vm.S", omit_sizes=('2d',))


def vector_logic_dn(name, operation):
    """ Generates a pair of gadgets for vector bitwise logic instructions. """
    with_dn(f"{name}_d", f"{operation} Vd.8b, Vn.8b")
    with_dn(f"{name}_q", f"{operation} Vd.16b, Vn.16b")


def vector_logic_dnm(name, operation):
    """ Generates a pair of gadgets for vector bitwise logic instructions. """
    with_dnm(f"{name}_d", f"{operation} Vd.8b, Vn.8b, Vm.8b")
    with_dnm(f"{name}_q", f"{operation} Vd.16b, Vn.16b, Vm.16b")


#
# Gadget definitions.
#

START_COLLECTION("misc")

# Call a C language helper function by address.
simple("call",
    # Get our C runtime function's location as a pointer-sized immediate...
    "ldr x27, [x28], #8",

    # Store our TB return address for our helper.
    "str x28, [x25]",

    # Prepare ourselves to call into our C runtime...
    *C_CALL_PROLOGUE,

    # ... perform the call itself ...
    "blr x27",

    # Save the result of our call for later.
    "mov x27, x0",

    # ... and restore our environment.
    *C_CALL_EPILOGUE,

    # Restore our return value.
    "mov x0, x27"
)

# Branch to a given immediate address.
simple("br",
    # Use our immediate argument as our new bytecode-pointer location.
    "ldr x28, [x28]"
)


# Exit from a translation buffer execution.
simple("exit_tb",

    # We have a single immediate argument, which contains our return code.
    # Place it into x0, as one would a return code.
    "ldr x0, [x28], #8",

    # And finally, return back to the code that invoked our gadget stream.
    "ret"
)

# Memory barriers.
simple("mb_all", "dmb ish")
simple("mb_st",  "dmb ishst")
simple("mb_ld",  "dmb ishld")




for condition in ARCH_CONDITION_CODES:

    START_COLLECTION("setcond")

    # Performs a comparison between two operands.
    with_dnm(f"setcond_i32_{condition}",
        "subs Wd, Wn, Wm",
        f"cset Wd, {condition}"
    )
    with_dnm(f"setcond_i64_{condition}",
        "subs Xd, Xn, Xm",
        f"cset Xd, {condition}"
    )

    #
    # NOTE: we use _dnm for the conditional branches, even though we don't
    # actually do anything different based on the d argument. This gemerates
    # effectively 16 identical `brcond` gadgets for each condition; which we
    # use in the backend to spread out the actual branch sources we use.
    #
    # This is a slight mercy for the branch predictor, as not every conditional
    # branch is funneled throught the same address.
    #

    START_COLLECTION("brcond")

    # Branches iff a given comparison is true.
    with_dnm(f'brcond_i32_{condition}',

        # Grab our immediate argument.
        "ldr x27, [x28], #8",

        # Perform our comparison...
        "subs wzr, Wn, Wm",

        # ... and our conditional branch, which selectively sets w28 (our "gadget pointer")
        # to the new location, if required.
        f"csel x28, x27, x28, {condition}"
    )

    # Branches iff a given comparison is true.
    with_dnm(f'brcond_i64_{condition}',

        # Grab our immediate argument.
        "ldr x27, [x28], #8",

        # Perform our comparison and conditional branch.
        "subs xzr, Xn, Xm",

        # ... and our conditional branch, which selectively sets w28 (our "gadget pointer")
        # to the new location, if required.
        f"csel x28, x27, x28, {condition}"
    )


START_COLLECTION("mov")


# MOV variants.
with_dn("mov_i32",     "mov Wd, Wn")
with_dn("mov_i64",     "mov Xd, Xn")
with_d("movi_i32", "ldr Wd, [x28], #8")
with_d("movi_i64", "ldr Xd, [x28], #8")

# Create MOV variants that have common constants built in to the gadget.
# This optimization helps costly reads from memories for simple operations.
with_d_immediate("movi_imm_i32", "mov Wd, #Ii", immediate_range=range(64))
with_d_immediate("movi_imm_i64", "mov Xd, #Ii", immediate_range=range(64))

START_COLLECTION("load_unsigned")

# LOAD variants.
# TODO: should the signed variants have X variants for _i64?
ldst_dn("ld8u",      "ldrb  Wd, [Xn, x27]")
ldst_dn("ld16u",     "ldrh  Wd, [Xn, x27]")
ldst_dn("ld32u",     "ldr   Wd, [Xn, x27]")
ldst_dn("ld_i64",    "ldr   Xd, [Xn, x27]")

START_COLLECTION("load_signed")

ldst_dn("ld8s_i32",  "ldrsb Wd, [Xn, x27]")
ldst_dn("ld8s_i64",  "ldrsb Xd, [Xn, x27]")
ldst_dn("ld16s_i32", "ldrsh Wd, [Xn, x27]")
ldst_dn("ld16s_i64", "ldrsh Xd, [Xn, x27]")
ldst_dn("ld32s_i64", "ldrsw Xd, [Xn, x27]")

START_COLLECTION("store")

# STORE variants.
ldst_dn("st8",         "strb  Wd, [Xn, x27]")
ldst_dn("st16",        "strh  Wd, [Xn, x27]")
ldst_dn("st_i32",      "str   Wd, [Xn, x27]")
ldst_dn("st_i64",      "str   Xd, [Xn, x27]")

# QEMU LD/ST are handled in our C runtime rather than with simple gadgets,
# as they're nontrivial.

START_COLLECTION("arithmetic")

# Trivial arithmetic.
math_dnm("add" , "add" )
math_dnm("sub" , "sub" )
math_dnm("mul" , "mul" )
math_dnm("div" , "sdiv")
math_dnm("divu", "udiv")

# Division remainder
with_dnm("rem_i32",  "sdiv w27, Wn, Wm", "msub Wd, w27, Wm, Wn")
with_dnm("rem_i64",  "sdiv x27, Xn, Xm", "msub Xd, x27, Xm, Xn")
with_dnm("remu_i32", "udiv w27, Wn, Wm", "msub Wd, w27, Wm, Wn")
with_dnm("remu_i64", "udiv x27, Xn, Xm", "msub Xd, x27, Xm, Xn")

START_COLLECTION("logical")

# Trivial logical.
math_dn( "not",  "mvn")
math_dn( "neg",  "neg")
math_dnm("and",  "and")
math_dnm("andc", "bic")
math_dnm("or",   "orr")
math_dnm("orc",  "orn")
math_dnm("xor",  "eor")
math_dnm("eqv",  "eon")
math_dnm("shl",  "lsl")
math_dnm("shr",  "lsr")
math_dnm("sar",  "asr")
math_dnm("rotr", "ror")

# AArch64 lacks a Rotate Left; so we instead rotate right by a negative.
with_dnm("rotl_i32", "neg w27, Wm", "ror Wd, Wn, w27")
with_dnm("rotl_i64", "neg w27, Wm", "ror Xd, Xn, x27")

# We'll synthesize several instructions that don't exist; since it's still faster
# to run these as gadgets.
with_dnm("nand_i32", "and Wd, Wn, Wm", "mvn Wd, Wd")
with_dnm("nand_i64", "and Xd, Xn, Xm", "mvn Xd, Xd")
with_dnm("nor_i32",  "orr Wd, Wn, Wm", "mvn Wd, Wd")
with_dnm("nor_i64",  "orr Xd, Xn, Xm", "mvn Xd, Xd")

START_COLLECTION("bitwise")

# Count leading zeroes, with a twist: QEMU requires us to provide
# a default value for when the argument is 0.
with_dnm("clz_i32",

    # Perform the core CLZ into w26.
    "clz w26, Wn",

    # Check Wn to see if it was zero
    "tst Wn, Wn",

    # If it was zero, accept the argument provided in Wm.
    # Otherwise, accept our result from w26.
    "csel Wd, Wm, w26, eq"
)
with_dnm("clz_i64",

    # Perform the core CLZ into w26.
    "clz x26, Xn",

    # Check Wn to see if it was zero
    "tst Xn, Xn",

    # If it was zero, accept the argument provided in Wm.
    # Otherwise, accept our result from w26.
    "csel Xd, Xm, x26, eq"
)


# Count trailing zeroes, with a twist: QEMU requires us to provide
# a default value for when the argument is 0.
with_dnm("ctz_i32",
    # Reverse our bits before performing our actual clz.
    "rbit w26, Wn",
    "clz w26, w26",

    # Check Wn to see if it was zero
    "tst Wn, Wn",

    # If it was zero, accept the argument provided in Wm.
    # Otherwise, accept our result from w26.
    "csel Wd, Wm, w26, eq"
)
with_dnm("ctz_i64",

    # Perform the core CLZ into w26.
    "rbit x26, Xn",
    "clz x26, x26",

    # Check Wn to see if it was zero
    "tst Xn, Xn",

    # If it was zero, accept the argument provided in Wm.
    # Otherwise, accept our result from w26.
    "csel Xd, Xm, x26, eq"
)


START_COLLECTION("extension")

# Numeric extension.
math_dn("ext8s",      "sxtb", source_is_wn=True)
with_dn("ext8u",      "and Xd, Xn, #0xff")
math_dn("ext16s",     "sxth", source_is_wn=True)
with_dn("ext16u",     "and Wd, Wn, #0xffff")
with_dn("ext32s_i64", "sxtw Xd, Wn")
with_dn("ext32u_i64", "mov Wd, Wn")

# Numeric extraction.
with_dn("extrl",      "mov Wd, Wn")
with_dn("extrh",      "lsr Xd, Xn, #32")

START_COLLECTION("byteswap")

# Byte swapping.
with_dn("bswap16",    "rev w27, Wn", "lsr Wd, w27, #16")
with_dn("bswap32",    "rev Wd, Wn")
with_dn("bswap64",    "rev Xd, Xn")


# Handlers for QEMU_LD, which handles guest <- host loads.
for subtype in ('aligned', 'unaligned', 'slowpath'):
    is_aligned  = (subtype == 'aligned')
    is_slowpath = (subtype == 'slowpath')

    START_COLLECTION(f"qemu_ld_{subtype}_unsigned_le")

    ld_thunk(f"qemu_ld_ub_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_ldub_mmu",
        fastpath_32b=["ldrb Wd, [Xn, x27]"], fastpath_64b=["ldrb Wd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    ld_thunk(f"qemu_ld_leuw_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_lduw_mmu",
        fastpath_32b=["ldrh Wd, [Xn, x27]"], fastpath_64b=["ldrh Wd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    ld_thunk(f"qemu_ld_leul_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_ldul_mmu",
        fastpath_32b=["ldr Wd, [Xn, x27]"], fastpath_64b=["ldr Wd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    ld_thunk(f"qemu_ld_leq_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_ldq_mmu",
        fastpath_32b=["ldr Xd, [Xn, x27]"], fastpath_64b=["ldr Xd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )

    START_COLLECTION(f"qemu_ld_{subtype}_signed_le")

    ld_thunk(f"qemu_ld_sb_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_ldub_mmu_signed",
        fastpath_32b=["ldrsb Wd, [Xn, x27]"], fastpath_64b=["ldrsb Xd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    ld_thunk(f"qemu_ld_lesw_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_lduw_mmu_signed",
        fastpath_32b=["ldrsh Wd, [Xn, x27]"], fastpath_64b=["ldrsh Xd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    ld_thunk(f"qemu_ld_lesl_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_ldul_mmu_signed",
        fastpath_32b=["ldrsw Xd, [Xn, x27]"], fastpath_64b=["ldrsw Xd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )

    # Special variant for the most common modes, as a speedup optimization.
    ld_thunk(f"qemu_ld_ub_{subtype}_mode02", is_aligned=is_aligned, slowpath_helper="helper_ldub_mmu",
        fastpath_32b=["ldrb Wd, [Xn, x27]"], fastpath_64b=["ldrb Wd, [Xn, x27]"],
        force_slowpath=is_slowpath, immediate=0x02
    )
    ld_thunk(f"qemu_ld_leq_{subtype}_mode32", is_aligned=is_aligned, slowpath_helper="helper_ldq_mmu",
        fastpath_32b=["ldr Xd, [Xn, x27]"], fastpath_64b=["ldr Xd, [Xn, x27]"],
        force_slowpath=is_slowpath, immediate=0x32
    )
    ld_thunk(f"qemu_ld_leq_{subtype}_mode3a", is_aligned=is_aligned, slowpath_helper="helper_ldq_mmu",
        fastpath_32b=["ldr Xd, [Xn, x27]"], fastpath_64b=["ldr Xd, [Xn, x27]"],
        force_slowpath=is_slowpath, immediate=0x3a
    )


# Handlers for QEMU_ST, which handles guest -> host stores.
for subtype in ('aligned', 'unaligned', 'slowpath'):
    is_aligned  = (subtype == 'aligned')
    is_slowpath = (subtype == 'slowpath')

    START_COLLECTION(f"qemu_st_{subtype}_le")

    st_thunk(f"qemu_st_ub_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_stb_mmu",
        fastpath_32b=["strb Wd, [Xn, x27]"], fastpath_64b=["strb Wd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    st_thunk(f"qemu_st_leuw_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_stw_mmu",
        fastpath_32b=["strh Wd, [Xn, x27]"], fastpath_64b=["strh Wd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    st_thunk(f"qemu_st_leul_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_stl_mmu",
        fastpath_32b=["str Wd, [Xn, x27]"], fastpath_64b=["str Wd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    st_thunk(f"qemu_st_leq_{subtype}", is_aligned=is_aligned, slowpath_helper="helper_stq_mmu",
        fastpath_32b=["str Xd, [Xn, x27]"], fastpath_64b=["str Xd, [Xn, x27]"],
        force_slowpath=is_slowpath,
    )
    
    # Special optimization for the most common modes.
    st_thunk(f"qemu_st_ub_{subtype}_mode02", is_aligned=is_aligned, slowpath_helper="helper_stb_mmu",
        fastpath_32b=["strb Wd, [Xn, x27]"], fastpath_64b=["strb Wd, [Xn, x27]"],
        force_slowpath=is_slowpath, immediate=0x02
    )
    st_thunk(f"qemu_st_leq_{subtype}_mode32", is_aligned=is_aligned, slowpath_helper="helper_stq_mmu",
        fastpath_32b=["str Xd, [Xn, x27]"], fastpath_64b=["str Xd, [Xn, x27]"],
        force_slowpath=is_slowpath, immediate=0x32
    )
    st_thunk(f"qemu_st_leq_{subtype}_mode3a", is_aligned=is_aligned, slowpath_helper="helper_stq_mmu",
        fastpath_32b=["str Xd, [Xn, x27]"], fastpath_64b=["str Xd, [Xn, x27]"],
        force_slowpath=is_slowpath, immediate=0x3a
    )


#
# SIMD/Vector ops
#

# SIMD MOVI instructions.
START_COLLECTION(f"simd_base")

# Unoptimized/unoptimizable load of a vector64; grabbing an immediate.
with_d("ldi_d", "ldr Dd, [x28], #8")
with_d("ldi_q", "ldr Qd, [x28], #16")

# General purpose reg -> vec rec loads
vector_dn("dup", "dup Vd.S, Wn")

# move vector -> GP reg
with_dn("umov_s0", "umov Wd, Vn.s[0]")
with_dn("umov_d0", "umov Xd, Vn.d[0]")

# mov GP reg -> vector
with_dn("ins_s0", "ins Vd.s[0], Wn")
with_dn("ins_d0", "ins Vd.d[0], Xn")


# Memory -> vec reg loads.
# The offset of the load is stored in a 64b immediate.

# Duplicating load.
# TODO: possibly squish the add into the ld1r, if that's valid?
vector_dn("dupm", "ldr x27, [x28], #8", "add x27, x27, Xn", "ld1r {Vd.S}, [x27]")

# Direct loads.
with_dn("ldr_d",  "ldr x27, [x28], #8", "ldr Dd, [Xn, x27]")
with_dn("ldr_q",  "ldr x27, [x28], #8", "ldr Qd, [Xn, x27]")

# vec -> reg stores.
# The offset of the stores is stored in a 64b immediate.
with_dn("str_d",  "ldr x27, [x28], #8", "str Dd, [Xn, x27]")
with_dn("str_q",  "ldr x27, [x28], #8", "str Qd, [Xn, x27]")


START_COLLECTION(f"simd_arithmetic")

vector_math_dnm("add",   "add")
vector_math_dnm("usadd", "uqadd")
vector_math_dnm("ssadd", "sqadd")
vector_math_dnm("sub",   "sub")
vector_math_dnm("ussub", "uqsub")
vector_math_dnm("sssub", "sqsub")
vector_math_dnm_no64("mul",  "mul")
vector_math_dnm_no64("smax", "smax")
vector_math_dnm_no64("smin", "smin")
vector_math_dnm_no64("umax", "umax")
vector_math_dnm_no64("umin", "umin")

START_COLLECTION(f"simd_logical")

vector_logic_dnm("and",  "and")
vector_logic_dnm("andc", "bic")
vector_logic_dnm("or",   "orr")
vector_logic_dnm("orc",  "orn")
vector_logic_dnm("xor",  "eor")
vector_logic_dn( "not",  "not")
vector_dn("neg", "neg Vd.S, Vn.S")
vector_dn("abs", "abs Vd.S, Vn.S")
vector_logic_dnm( "bit",  "bit")
vector_logic_dnm( "bif",  "bif")
vector_logic_dnm( "bsl",  "bsl")

vector_math_dnm("shlv", "ushl")
vector_math_dnm("sshl", "sshl")

vector_dnm("cmeq", "cmeq Vd.S, Vn.S, Vm.S", scalar="cmeq Dd, Dn, Dm")
vector_dnm("cmgt", "cmgt Vd.S, Vn.S, Vm.S", scalar="cmgt Dd, Dn, Dm")
vector_dnm("cmge", "cmge Vd.S, Vn.S, Vm.S", scalar="cmge Dd, Dn, Dm")
vector_dnm("cmhi", "cmhi Vd.S, Vn.S, Vm.S", scalar="cmhi Dd, Dn, Dm")
vector_dnm("cmhs", "cmhs Vd.S, Vn.S, Vm.S", scalar="cmhs Dd, Dn, Dm")

START_COLLECTION(f"simd_immediate")

# Simple imm8 movs...
with_d_immediate("movi_cmode_e_op0_q0",  "movi Vd.8b, #Ii",          immediate_range=range(256))
with_d_immediate("movi_cmode_e_op0_q1",  "movi Vd.16b, #Ii",         immediate_range=range(256))

# ... all 00/FF movs...
with_d_immediate("movi_cmode_e_op1_q0",  "movi Dd, #Si",             immediate_range=range(256))
with_d_immediate("movi_cmode_e_op1_q1",  "movi Vd.2d, #Si",          immediate_range=range(256))

# Halfword MOVs.
with_d_immediate("movi_cmode_8_op0_q0",  "movi Vd.4h, #Ii",         immediate_range=range(256))
with_d_immediate("movi_cmode_8_op0_q1",  "movi Vd.8h, #Ii",         immediate_range=range(256))
with_d_immediate("mvni_cmode_8_op0_q0",  "mvni Vd.4h, #Ii",         immediate_range=range(256))
with_d_immediate("mvni_cmode_8_op0_q1",  "mvni Vd.8h, #Ii",         immediate_range=range(256))
with_d_immediate("movi_cmode_a_op0_q0",  "movi Vd.4h, #Ii, lsl #8", immediate_range=range(256))
with_d_immediate("movi_cmode_a_op0_q1",  "movi Vd.8h, #Ii, lsl #8", immediate_range=range(256))
with_d_immediate("mvni_cmode_a_op0_q0",  "mvni Vd.4h, #Ii, lsl #8", immediate_range=range(256))
with_d_immediate("mvni_cmode_a_op0_q1",  "mvni Vd.8h, #Ii, lsl #8", immediate_range=range(256))

# Halfword ORIs, for building complex MOVs.
with_d_immediate("orr_cmode_a_op0_q0",   "orr Vd.4h, #Ii, lsl #8",  immediate_range=range(256))
with_d_immediate("orr_cmode_a_op0_q1",   "orr Vd.8h, #Ii, lsl #8",  immediate_range=range(256))


# Print a list of output files generated.
output_c_filenames = (f"'tcti_{name}_gadgets.c'" for name in output_files.keys())
output_h_filenames = (f"'tcti_{name}_gadgets.h'" for name in output_files.keys())

print("Sources generated:",    file=sys.stderr)
print(f"gadgets = [",          file=sys.stderr)
print("      tcti_gadgets.h,", file=sys.stderr)

for name in output_files.keys():
    print(f"      'tcti_{name}_gadgets.c',", file=sys.stderr)
    print(f"      'tcti_{name}_gadgets.h',", file=sys.stderr)

print(f"]", file=sys.stderr)

# Statistics.
sys.stderr.write(f"\nGenerated {gadgets} gadgets with {instructions} instructions (~{(instructions * 4) // 1024 // 1024} MiB).\n\n")
