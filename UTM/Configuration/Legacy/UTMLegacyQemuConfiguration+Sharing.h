//
// Copyright © 2020 osy. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#import "UTMLegacyQemuConfiguration.h"

NS_ASSUME_NONNULL_BEGIN

@interface UTMLegacyQemuConfiguration (Sharing)

@property (nonatomic, assign) BOOL shareClipboardEnabled;
@property (nonatomic, assign) BOOL shareDirectoryEnabled;
@property (nonatomic, assign) BOOL shareDirectoryReadOnly;
@property (nonatomic, nullable, copy) NSString *shareDirectoryName;
@property (nonatomic, nullable, copy) NSData *shareDirectoryBookmark;
@property (nonatomic, assign) BOOL usb3Support;
@property (nonatomic, nullable, copy) NSNumber *usbRedirectionMaximumDevices;

- (void)migrateSharingConfigurationIfNecessary;

@end

NS_ASSUME_NONNULL_END
