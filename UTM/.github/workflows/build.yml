name: Build
on:
  push:
    branches:
      - '**'
    tags-ignore:
      - '**'
    paths-ignore:
      - 'LICENSE'
      - '**.md'
  pull_request:
  release:
    types: [created]
  workflow_dispatch:
    inputs:
      test_release:
        description: 'Test release?'
        required: true
        default: 'false'
      rebuild_sysroot:
        description: 'Force rebuild sysroot?'
        required: true
        default: 'false'

env:
  BUILD_XCODE_PATH: /Applications/Xcode_26.0.app
  RUNNER_IMAGE: macos-15
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 60

jobs:
  configuration:
    name: Setup configuration
    runs-on: ubuntu-latest
    outputs:
      runner: ${{ steps.checker.outputs.runners }}
      github-runner: ${{ steps.checker.outputs.github-runner }}
    steps:
    - name: Check for hosted runners
      id: checker
      shell: bash
      env:
        IS_SELF_HOSTED_RUNNER: ${{ vars.IS_SELF_HOSTED_RUNNER || (github.repository_owner == 'utmapp' && 'true') }}
      run: |
        echo "github-runner='$RUNNER_IMAGE'" >> $GITHUB_OUTPUT
        if [ "$IS_SELF_HOSTED_RUNNER" == "true" ]; then
          echo "runners=['self-hosted', 'macOS']" >> $GITHUB_OUTPUT
        else
          echo "runners='$RUNNER_IMAGE'" >> $GITHUB_OUTPUT
        fi
  build-sysroot:
    name: Build Sysroot
    runs-on: ${{ fromJSON(needs.configuration.outputs.runner) }}
    needs: configuration
    strategy:
      matrix:
        arch: [arm64]
        platform: [ios, ios_simulator, ios-tci, ios_simulator-tci, macos, visionos, visionos_simulator, visionos-tci, visionos_simulator-tci]
        include:
          # x86_64 supported only for macOS and simulators
          - arch: x86_64
            platform: macos
          - arch: x86_64
            platform: ios_simulator
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
      - name: Setup Xcode
        shell: bash
        run: |
          [[ "$(xcode-select -p)" == "${{ env.BUILD_XCODE_PATH }}"* ]] || sudo xcode-select -s "${{ env.BUILD_XCODE_PATH }}"
      - name: Check Cache
        id: cache-sysroot
        uses: actions/cache/restore@v4
        with:
          path: ./sysroot-${{ matrix.platform }}-${{ matrix.arch }}
          key: ${{ matrix.platform }}-${{ matrix.arch }}-${{ hashFiles('scripts/build_dependencies.sh') }}-${{ hashFiles('patches/**') }}-${{ github.sha }}
          restore-keys: ${{ matrix.platform }}-${{ matrix.arch }}-${{ hashFiles('scripts/build_dependencies.sh') }}-${{ hashFiles('patches/**') }}
          lookup-only: github.event_name != 'release' && github.event.inputs.test_release != 'true'
      - name: Setup Path
        shell: bash
        run: |
          echo "/usr/local/opt/bison/bin:/opt/homebrew/opt/bison/bin" >> $GITHUB_PATH
      - name: Install Requirements
        if: (steps.cache-sysroot.outputs.cache-matched-key == '' || github.event.inputs.rebuild_sysroot == 'true') && needs.configuration.outputs.runner == env.RUNNER_IMAGE
        run: |
          brew uninstall cmake
          brew install bison pkg-config gettext glib-utils libgpg-error nasm make meson
          pip3 install --user six pyparsing
          rm -f /usr/local/lib/pkgconfig/*.pc
      - name: Build Sysroot
        if: steps.cache-sysroot.outputs.cache-matched-key == '' || github.event.inputs.rebuild_sysroot == 'true'
        run: ./scripts/build_dependencies.sh -p ${{ matrix.platform }} -a ${{ matrix.arch }}
        env:
          NCPU: ${{ endsWith(matrix.platform, '-tci') && '1' || '0' }} # limit 1 CPU for TCI build due to memory issues, 0 = unlimited for other builds
      - name: Compress Sysroot
        if: steps.cache-sysroot.outputs.cache-matched-key == '' || github.event_name == 'release' || github.event.inputs.test_release == 'true' || github.event.inputs.rebuild_sysroot == 'true'
        run: tar -acf sysroot.tgz sysroot*
      - name: Upload Sysroot
        if: steps.cache-sysroot.outputs.cache-matched-key == '' || github.event_name == 'release' || github.event.inputs.test_release == 'true' || github.event.inputs.rebuild_sysroot == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: Sysroot-${{ matrix.platform }}-${{ matrix.arch }}
          path: sysroot.tgz
      - name: Save Cache
        if: steps.cache-sysroot.outputs.cache-matched-key == ''
        uses: actions/cache/save@v4
        with:
          path: ./sysroot-${{ matrix.platform }}-${{ matrix.arch }}
          key: ${{ steps.cache-sysroot.outputs.cache-primary-key }}
          upload-chunk-size: 1048576 # 1 MiB
  build-sysroot-universal:
    name: Build Sysroot (Universal Mac)
    runs-on: ${{ fromJSON(needs.configuration.outputs.github-runner) }}
    needs: [configuration, build-sysroot]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Check Cache (Universal Mac)
        id: cache-sysroot-universal
        uses: actions/cache/restore@v4
        with:
          path: ./sysroot-macOS-arm64_x86_64
          key: macos-universal-${{ hashFiles('scripts/build_dependencies.sh', 'scripts/pack_dependencies.sh') }}-${{ hashFiles('patches/**') }}-${{ github.sha }}
          restore-keys: macos-universal-${{ hashFiles('scripts/build_dependencies.sh', 'scripts/pack_dependencies.sh') }}-${{ hashFiles('patches/**') }}
          lookup-only: github.event_name != 'release' && github.event.inputs.test_release != 'true'
      - name: Cache Sysroot (arm64)
        if: steps.cache-sysroot-universal.outputs.cache-matched-key == ''
        id: cache-sysroot-arm64
        uses: actions/cache/restore@v4
        with:
          path: ./sysroot-macos-arm64
          key: macos-arm64-${{ hashFiles('scripts/build_dependencies.sh') }}-${{ hashFiles('patches/**') }}
          fail-on-cache-miss: true
      - name: Cache Sysroot (x86_64)
        if: steps.cache-sysroot-universal.outputs.cache-matched-key == ''
        id: cache-sysroot-x86_64
        uses: actions/cache/restore@v4
        with:
          path: ./sysroot-macos-x86_64
          key: macos-x86_64-${{ hashFiles('scripts/build_dependencies.sh') }}-${{ hashFiles('patches/**') }}
          fail-on-cache-miss: true
      - name: Pack Universal Sysroot
        if: steps.cache-sysroot-universal.outputs.cache-matched-key == ''
        run: |
          ./scripts/pack_dependencies.sh . macos arm64 x86_64
      - name: Compress Sysroot
        if: steps.cache-sysroot-universal.outputs.cache-matched-key == '' || github.event_name == 'release' || github.event.inputs.test_release == 'true' || github.event.inputs.rebuild_sysroot == 'true'
        run: tar -acf sysroot.tgz sysroot-macOS-arm64_x86_64
      - name: Upload Sysroot
        if: steps.cache-sysroot-universal.outputs.cache-matched-key == '' || github.event_name == 'release' || github.event.inputs.test_release == 'true' || github.event.inputs.rebuild_sysroot == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: Sysroot-macos-universal
          path: sysroot.tgz
      - name: Save Cache (Universal Mac)
        if: steps.cache-sysroot-universal.outputs.cache-matched-key == ''
        uses: actions/cache/save@v4
        with:
          path: ./sysroot-macOS-arm64_x86_64
          key: ${{ steps.cache-sysroot-universal.outputs.cache-primary-key }}
          upload-chunk-size: 1048576 # 1 MiB
  build-utm:
    name: Build UTM
    runs-on: ${{ fromJSON(needs.configuration.outputs.runner) }}
    needs: [configuration, build-sysroot]
    strategy:
      matrix:
        configuration: [
          {arch: "arm64", sdk: "iphoneos", platform: "ios", scheme: "iOS"},
          {arch: "arm64", sdk: "iphoneos", platform: "ios-tci", scheme: "iOS-SE"},
          {arch: "arm64", sdk: "iphoneos", platform: "ios-tci", scheme: "iOS-Remote"},
          {arch: "arm64", sdk: "xros", platform: "visionos", scheme: "iOS"},
          {arch: "arm64", sdk: "xros", platform: "visionos-tci", scheme: "iOS-SE"},
          {arch: "arm64", sdk: "xros", platform: "visionos-tci", scheme: "iOS-Remote"},
          {arch: "arm64", sdk: "macosx", platform: "macos", scheme: "macOS"},
          {arch: "x86_64", sdk: "macosx", platform: "macos", scheme: "macOS"},
        ]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
      - name: Cache Sysroot
        id: cache-sysroot
        uses: actions/cache/restore@v4
        with:
          path: ./sysroot-${{ matrix.configuration.platform }}-${{ matrix.configuration.arch }}
          key: ${{ matrix.configuration.platform }}-${{ matrix.configuration.arch }}-${{ hashFiles('scripts/build_dependencies.sh') }}-${{ hashFiles('patches/**') }}
          fail-on-cache-miss: true
      - name: Setup Xcode
        shell: bash
        run: |
          [[ "$(xcode-select -p)" == "${{ env.BUILD_XCODE_PATH }}"* ]] || sudo xcode-select -s "${{ env.BUILD_XCODE_PATH }}"
      - name: Build UTM
        run: |
          ./scripts/build_utm.sh -k ${{ matrix.configuration.sdk }} -s ${{ matrix.configuration.scheme }} -a ${{ matrix.configuration.arch }} -o UTM
          tar -acf UTM.xcarchive.tgz UTM.xcarchive
      - name: Upload UTM
        uses: actions/upload-artifact@v4
        with:
          name: UTM-${{ matrix.configuration.scheme }}-${{ matrix.configuration.platform }}-${{ matrix.configuration.arch }}
          path: UTM.xcarchive.tgz
  build-universal:
    name: Build UTM (Universal Mac)
    runs-on: ${{ fromJSON(needs.configuration.outputs.runner) }}
    needs: [configuration, build-sysroot-universal]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
      - name: Cache Sysroot
        id: cache-sysroot
        uses: actions/cache/restore@v4
        with:
          path: ./sysroot-macOS-arm64_x86_64
          key: macos-universal-${{ hashFiles('scripts/build_dependencies.sh', 'scripts/pack_dependencies.sh') }}-${{ hashFiles('patches/**') }}
          fail-on-cache-miss: true
      - name: Setup Xcode
        shell: bash
        run: |
          [[ "$(xcode-select -p)" == "${{ env.BUILD_XCODE_PATH }}"* ]] || sudo xcode-select -s "${{ env.BUILD_XCODE_PATH }}"
      - name: Build UTM
        run: |
          ./scripts/build_utm.sh -t "$SIGNING_TEAM_ID" -k macosx -s macOS -a "arm64 x86_64" -o UTM
          tar -acf UTM.xcarchive.tgz UTM.xcarchive
        env:
          SIGNING_TEAM_ID: ${{ vars.SIGNING_TEAM_ID }}
      - name: Upload UTM
        uses: actions/upload-artifact@v4
        with:
          name: UTM-macos-universal
          path: UTM.xcarchive.tgz
  package-ios:
    name: Package (iOS)
    runs-on: ${{ fromJSON(needs.configuration.outputs.github-runner) }}
    needs: [configuration, build-utm]
    strategy:
      matrix:
        configuration: [
          {platform: "ios", scheme: "iOS", mode: "ipa", name: "UTM.ipa", path: "UTM.ipa"},
          {platform: "ios-tci", scheme: "iOS-SE", mode: "ipa-se", name: "UTM-SE.ipa", path: "UTM SE.ipa"},
          {platform: "ios", scheme: "iOS", mode: "ipa-hv", name: "UTM-HV.ipa", path: "UTM.ipa"},
          {platform: "ios", scheme: "iOS", mode: "deb", name: "UTM.deb", path: "UTM.deb"},
          {platform: "visionos", scheme: "iOS", mode: "ipa", name: "UTM-visionOS.ipa", path: "UTM.ipa"},
          {platform: "visionos-tci", scheme: "iOS-SE", mode: "ipa-se", name: "UTM-SE-visionOS.ipa", path: "UTM SE.ipa"},
          {platform: "ios-tci", scheme: "iOS-Remote", mode: "ipa-remote", name: "UTM-Remote.ipa", path: "UTM Remote.ipa"},
          {platform: "visionos-tci", scheme: "iOS-Remote", mode: "ipa-remote", name: "UTM-Remote-visionOS.ipa", path: "UTM Remote.ipa"},
        ]
    if: github.event_name == 'release' || github.event.inputs.test_release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: UTM-${{ matrix.configuration.scheme }}-${{ matrix.configuration.platform }}-arm64
      - name: Install ldid + dpkg
        run: brew install ldid dpkg
      - name: Fakesign IPA
        run: |
          tar -xf UTM.xcarchive.tgz
          ./scripts/package.sh ${{ matrix.configuration.mode }} UTM.xcarchive .
      - name: Upload Artifact
        if: github.event_name != 'release'
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.configuration.name }}
          path: ${{ matrix.configuration.path }}
      - name: Upload Release Asset
        if: github.event_name == 'release'
        uses: actions/upload-release-asset@v1.0.2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: ${{ matrix.configuration.path }}
          asset_name: ${{ matrix.configuration.name }}
          asset_content_type: application/octet-stream
  dispatch-ios:
    name: Dispatch (iOS)
    runs-on: ubuntu-latest
    needs: package-ios
    if: github.event_name == 'release'
    steps:
      - name: Update AltStore Repository
        continue-on-error: true
        uses: peter-evans/repository-dispatch@v1
        with:
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          repository: ${{ vars.DISPATCH_ALTSTORE_REPO_NAME }}
          event-type: new-release
      - name: Update Cydia Repository
        continue-on-error: true
        uses: peter-evans/repository-dispatch@v1
        with:
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          repository: ${{ vars.DISPATCH_CYDIA_REPO_NAME }}
          event-type: new-release
  package-mac:
    name: Package (macOS)
    runs-on: ${{ fromJSON(needs.configuration.outputs.github-runner) }}
    needs: [configuration, build-universal]
    if: github.event_name == 'release' || github.event.inputs.test_release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Xcode
        shell: bash
        run: |
          [[ "$(xcode-select -p)" == "${{ env.BUILD_XCODE_PATH }}"* ]] || sudo xcode-select -s "${{ env.BUILD_XCODE_PATH }}"
      - name: Import signing certificate into keychain
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.SIGNING_CERTIFICATE_P12_DATA }}
          p12-password: ${{ secrets.SIGNING_CERTIFICATE_PASSWORD }}
      - name: Import App Store Connect API Key
        run: |
          mkdir -p ~/.appstoreconnect/private_keys
          echo $AUTHKEY_API_KEY | base64 --decode -o ~/.appstoreconnect/private_keys/AuthKey_$API_KEY.p8
        env:
          AUTHKEY_API_KEY: ${{ secrets.CONNECT_KEY }}
          API_KEY: ${{ vars.CONNECT_KEY_ID }}
      - name: Install Provisioning Profiles
        run: |
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo $PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$PROFILE_UUID.provisionprofile
          echo $HELPER_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$HELPER_PROFILE_UUID.provisionprofile
          echo $LAUNCHER_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$LAUNCHER_PROFILE_UUID.provisionprofile
        env:
          PROFILE_DATA: ${{ vars.PROFILE_DATA }}
          PROFILE_UUID: ${{ vars.PROFILE_UUID }}
          HELPER_PROFILE_DATA: ${{ vars.HELPER_PROFILE_DATA }}
          HELPER_PROFILE_UUID: ${{ vars.HELPER_PROFILE_UUID }}
          LAUNCHER_PROFILE_DATA: ${{ vars.LAUNCHER_PROFILE_DATA }}
          LAUNCHER_PROFILE_UUID: ${{ vars.LAUNCHER_PROFILE_UUID }}
      - name: Install appdmg
        run: npm install -g appdmg
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: UTM-macos-universal
      - name: Package for Release
        run: |
          tar -xf UTM.xcarchive.tgz
          ./scripts/package_mac.sh developer-id UTM.xcarchive . "$SIGNING_TEAM_ID" "$PROFILE_UUID" "$HELPER_PROFILE_UUID" "$LAUNCHER_PROFILE_UUID"
        env:
          SIGNING_TEAM_ID: ${{ vars.SIGNING_TEAM_ID }}
          PROFILE_UUID: ${{ vars.PROFILE_UUID }}
          HELPER_PROFILE_UUID: ${{ vars.HELPER_PROFILE_UUID }}
          LAUNCHER_PROFILE_UUID: ${{ vars.LAUNCHER_PROFILE_UUID }}
      - name: Notarize app
        run: |
          xcrun notarytool submit --issuer "$ISSUER_UUID" --key-id "$API_KEY" --key "~/.appstoreconnect/private_keys/AuthKey_$API_KEY.p8" --team-id "$SIGNING_TEAM_ID" --wait "UTM.dmg"
          xcrun stapler staple "UTM.dmg"
        env:
          SIGNING_TEAM_ID: ${{ vars.SIGNING_TEAM_ID }}
          ISSUER_UUID: ${{ vars.CONNECT_ISSUER_ID }}
          API_KEY: ${{ vars.CONNECT_KEY_ID }}
      - name: Upload Artifact
        if: github.event_name != 'release'
        uses: actions/upload-artifact@v4
        with:
          name: UTM-dmg
          path: UTM.dmg
      - name: Upload Release Asset
        if: github.event_name == 'release'
        uses: actions/upload-release-asset@v1.0.2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: UTM.dmg
          asset_name: UTM.dmg
          asset_content_type: application/octet-stream
  submit-mac:
    name: Submit (macOS)
    runs-on: ${{ fromJSON(needs.configuration.outputs.github-runner) }}
    needs: [configuration, build-universal]
    if: github.event_name == 'release' || github.event.inputs.test_release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Xcode
        shell: bash
        run: |
          [[ "$(xcode-select -p)" == "${{ env.BUILD_XCODE_PATH }}"* ]] || sudo xcode-select -s "${{ env.BUILD_XCODE_PATH }}"
      - name: Import signing certificate into keychain
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.SIGNING_CERTIFICATE_P12_DATA }}
          p12-password: ${{ secrets.SIGNING_CERTIFICATE_PASSWORD }}
      - name: Import App Store Connect API Key
        run: |
          mkdir -p ~/.appstoreconnect/private_keys
          echo $AUTHKEY_API_KEY | base64 --decode -o ~/.appstoreconnect/private_keys/AuthKey_$API_KEY.p8
        env:
          AUTHKEY_API_KEY: ${{ secrets.CONNECT_KEY }}
          API_KEY: ${{ vars.CONNECT_KEY_ID }}
      - name: Install Provisioning Profiles
        run: |
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo $PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$PROFILE_UUID.provisionprofile
          echo $HELPER_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$HELPER_PROFILE_UUID.provisionprofile
          echo $LAUNCHER_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$LAUNCHER_PROFILE_UUID.provisionprofile
        env:
          PROFILE_DATA: ${{ vars.APP_STORE_PROFILE_DATA }}
          PROFILE_UUID: ${{ vars.APP_STORE_PROFILE_UUID }}
          HELPER_PROFILE_DATA: ${{ vars.APP_STORE_HELPER_PROFILE_DATA }}
          HELPER_PROFILE_UUID: ${{ vars.APP_STORE_HELPER_PROFILE_UUID }}
          LAUNCHER_PROFILE_DATA: ${{ vars.APP_STORE_LAUNCHER_PROFILE_DATA }}
          LAUNCHER_PROFILE_UUID: ${{ vars.APP_STORE_LAUNCHER_PROFILE_UUID }}
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: UTM-macos-universal
      - name: Package for App Store
        run: |
          tar -xf UTM.xcarchive.tgz
          ./scripts/package_mac.sh app-store UTM.xcarchive . "$SIGNING_TEAM_ID" "$PROFILE_UUID" "$HELPER_PROFILE_UUID" "$LAUNCHER_PROFILE_UUID"
        env:
          SIGNING_TEAM_ID: ${{ vars.SIGNING_TEAM_ID }}
          PROFILE_UUID: ${{ vars.APP_STORE_PROFILE_UUID }}
          HELPER_PROFILE_UUID: ${{ vars.APP_STORE_HELPER_PROFILE_UUID }}
          LAUNCHER_PROFILE_UUID: ${{ vars.APP_STORE_LAUNCHER_PROFILE_UUID }}
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: UTM-pkg
          path: UTM.pkg
      - name: Upload app to App Store Connect
        if: github.event_name == 'release'
        run: |
          xcrun altool --upload-app -t macos -f "UTM.pkg" --apiKey "$API_KEY" --apiIssuer "$ISSUER_UUID"
        env:
          ISSUER_UUID: ${{ vars.CONNECT_ISSUER_ID }}
          API_KEY: ${{ vars.CONNECT_KEY_ID }}
  submit-ios:
    name: Submit (iOS)
    runs-on: ${{ fromJSON(needs.configuration.outputs.github-runner) }}
    needs: [configuration, build-utm]
    strategy:
      matrix:
        configuration: [
          {platform: "ios-tci", scheme: "iOS-SE", mode: "ipa-se-signed", name: "UTM-SE-signed.ipa", path: "UTM SE.ipa", type: "ios"},
          {platform: "visionos-tci", scheme: "iOS-SE", mode: "ipa-se-signed", name: "UTM-SE-visionOS-signed.ipa", path: "UTM SE.ipa", type: "visionos"},
          {platform: "ios-tci", scheme: "iOS-Remote", mode: "ipa-remote-signed", name: "UTM-Remote-signed.ipa", path: "UTM Remote.ipa", type: "ios"},
          {platform: "visionos-tci", scheme: "iOS-Remote", mode: "ipa-remote-signed", name: "UTM-Remote-visionOS-signed.ipa", path: "UTM Remote.ipa", type: "visionos"},
        ]
    if: github.event_name == 'release' || github.event.inputs.test_release == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Xcode
        shell: bash
        run: |
          [[ "$(xcode-select -p)" == "${{ env.BUILD_XCODE_PATH }}"* ]] || sudo xcode-select -s "${{ env.BUILD_XCODE_PATH }}"
      - name: Import signing certificate into keychain
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.SIGNING_CERTIFICATE_P12_DATA }}
          p12-password: ${{ secrets.SIGNING_CERTIFICATE_PASSWORD }}
      - name: Import App Store Connect API Key
        run: |
          mkdir -p ~/.appstoreconnect/private_keys
          echo $AUTHKEY_API_KEY | base64 --decode -o ~/.appstoreconnect/private_keys/AuthKey_$API_KEY.p8
        env:
          AUTHKEY_API_KEY: ${{ secrets.CONNECT_KEY }}
          API_KEY: ${{ vars.CONNECT_KEY_ID }}
      - name: Install Provisioning Profiles
        run: |
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo $IOS_REMOTE_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$IOS_REMOTE_PROFILE_UUID.provisionprofile
          echo $IOS_SE_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$IOS_SE_PROFILE_UUID.provisionprofile
          echo $LAUNCHER_PROFILE_DATA | base64 --decode -o ~/Library/MobileDevice/Provisioning\ Profiles/$LAUNCHER_PROFILE_UUID.provisionprofile
        env:
          IOS_REMOTE_PROFILE_DATA: ${{ vars.IOS_REMOTE_PROFILE_DATA }}
          IOS_REMOTE_PROFILE_UUID: ${{ vars.IOS_REMOTE_PROFILE_UUID }}
          IOS_SE_PROFILE_DATA: ${{ vars.IOS_SE_PROFILE_DATA }}
          IOS_SE_PROFILE_UUID: ${{ vars.IOS_SE_PROFILE_UUID }}
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: UTM-${{ matrix.configuration.scheme }}-${{ matrix.configuration.platform }}-arm64
      - name: Package for App Store
        run: |
          tar -xf UTM.xcarchive.tgz
          ./scripts/package.sh ${{ matrix.configuration.mode }} UTM.xcarchive . "$SIGNING_TEAM_ID" "$PROFILE_UUID" app-store
        env:
          SIGNING_TEAM_ID: ${{ vars.SIGNING_TEAM_ID }}
          PROFILE_UUID: ${{ matrix.configuration.scheme == 'iOS-Remote' && vars.IOS_REMOTE_PROFILE_UUID || vars.IOS_SE_PROFILE_UUID }}
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.configuration.name }}
          path: ${{ matrix.configuration.path }}
      - name: Upload app to App Store Connect
        if: github.event_name == 'release'
        run: |
          xcrun altool --upload-app -t "$TYPE" -f "$FILE" --apiKey "$API_KEY" --apiIssuer "$ISSUER_UUID"
        env:
          FILE: ${{ matrix.configuration.path }}
          TYPE: ${{ matrix.configuration.type }}
          ISSUER_UUID: ${{ vars.CONNECT_ISSUER_ID }}
          API_KEY: ${{ vars.CONNECT_KEY_ID }}
