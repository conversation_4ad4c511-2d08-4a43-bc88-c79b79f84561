/* (No Comment) */
"Apple Pencil Input" = "Apple Pencil 输入";

/* (No Comment) */
"Auto save on background" = "在后台运行时自动保存";

/* (No Comment) */
"Auto save on low memory" = "在内存不足时自动保存";

/* (No Comment) */
"Background" = "后台";

/* (No Comment) */
"Backspace" = "退格";

/* (No Comment) */
"Caps" = "大写锁定";

/* (No Comment) */
"Click & Hold" = "点击并按住";

/* (No Comment) */
"Continue running VM in the background" = "在后台继续运行虚拟机";

/* (No Comment) */
"Cursor" = "光标";

/* (No Comment) */
"D-DOWN" = "下方向键";

/* (No Comment) */
"D-LEFT" = "左方向键";

/* (No Comment) */
"D-RIGHT" = "右方向键";

/* (No Comment) */
"D-UP" = "上方向键";

/* (No Comment) */
"Disabled" = "禁用";

/* (No Comment) */
"Down" = "下";

/* (No Comment) */
"Drag cursor" = "拖动光标";

/* (No Comment) */
"Enabled" = "启用";

/* (No Comment) */
"Enter" = "回车";

/* (No Comment) */
"Follow cursor" = "跟随光标";

/* (No Comment) */
"Gamepad" = "手柄";

/* (No Comment) */
"Gamepad - Cursor Speed" = "手柄 - 光标速度";

/* (No Comment) */
"Gestures" = "手势";

/* A single strings file, whose title is specified in your preferences schema. The strings files provide the localized content to display to the user for each of your preferences. */
"Group" = "组";

/* (No Comment) */
"Left" = "左";

/* (No Comment) */
"Long Press" = "长按";

/* (No Comment) */
"Menu" = "菜单";

/* (No Comment) */
"Mouse Left Button" = "鼠标左键";

/* (No Comment) */
"Mouse Middle Button" = "鼠标中键";

/* (No Comment) */
"Mouse Right Button" = "鼠标右键";

/* (No Comment) */
"Mouse Wheel" = "鼠标滚轮";

/* (No Comment) */
"Mouse Wheel (per swipe)" = "鼠标滚轮 (每次滚动)";

/* (No Comment) */
"Move Screen" = "移动显示屏";

/* (No Comment) */
"Name" = "名称";

/* (No Comment) */
"none given" = "未指定";

/* (No Comment) */
"Right" = "右";

/* (No Comment) */
"Right Click" = "右键单击";

/* (No Comment) */
"Space" = "空格";

/* (No Comment) */
"Tablet mode (always show cursor)" = "平板模式 (始终显示光标)";

/* (No Comment) */
"Tablet mode (try hiding cursor)" = "平板模式 (尝试隐藏光标)";

/* (No Comment) */
"Three Finger Pan" = "三指拖移";

/* (No Comment) */
"Touch Input" = "触摸输入";

/* (No Comment) */
"Touch mode (always show cursor)" = "触摸模式 (始终显示光标)";

/* (No Comment) */
"Touch mode (try hiding cursor)" = "触摸模式 (尝试隐藏光标)";

/* (No Comment) */
"Touchpad/Mouse Input" = "触控板/鼠标输入";

/* (No Comment) */
"Two Finger Pan" = "双指拖移";

/* (No Comment) */
"Two Finger Scroll" = "双指滚动";

/* (No Comment) */
"Two Finger Swipe" = "双指轻扫";

/* (No Comment) */
"Two Finger Tap" = "双指轻点";

/* (No Comment) */
"Up" = "上";

// Additional Strings (unable to be extracted by Xcode)

/* (No Comment) */
"About" = "关于";

/* (No Comment) */
"Build" = "构建";

/* (No Comment) */
"Cursor - Drag Speed" = "指针 - 拖放速度";

/* (No Comment) */
"Cursor - Scroll Wheel" = "指针 - 滚轮";

/* (No Comment) */
"Default" = "默认";

/* (No Comment) */
"Devices" = "设备";

/* (No Comment) */
"Disable screen dimming when idle" = "闲置时禁用屏幕变暗";

/* (No Comment) */
"Do not save VM screenshot to disk" = "不将虚拟机截图保存到磁盘";

/* (No Comment) */
"FPS Limit" = "FPS 上限";

/* (No Comment) */
"Graphics" = "图形";

/* (No Comment) */
"Idle" = "闲置";

/* (No Comment) */
"Invert Scroll" = "反转滚动";

/* (No Comment) */
"License" = "许可";

/* (No Comment) */
"Prefer device to external microphone" = "偏好外置麦克风设备";

/* (No Comment) */
"Renderer Backend" = "渲染器后端";

/* (No Comment) */
"Version" = "版本";
