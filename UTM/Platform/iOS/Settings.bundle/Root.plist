<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>StringsTable</key>
	<string>Root</string>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Background</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Continue running VM in the background</string>
			<key>Key</key>
			<string>RunInBackground</string>
			<key>DefaultValue</key>
			<false/>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
			</array>
			<key>Platform</key>
			<string>iOS</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Auto save on background</string>
			<key>Key</key>
			<string>AutosaveBackground</string>
			<key>DefaultValue</key>
			<true/>
			<key>Platform</key>
			<string>iOS</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Auto save on low memory</string>
			<key>Key</key>
			<string>AutosaveLowMemory</string>
			<key>DefaultValue</key>
			<true/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Idle</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Disable screen dimming when idle</string>
			<key>Key</key>
			<string>DisableIdleTimer</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Do not save VM screenshot to disk</string>
			<key>Key</key>
			<string>NoSaveScreenshot</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Devices</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Do not show prompt when USB device is plugged in</string>
			<key>Key</key>
			<string>NoUsbPrompt</string>
			<key>DefaultValue</key>
			<false/>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
				<string>iOS-SE</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Prefer device to external microphone</string>
			<key>Key</key>
			<string>PreferDeviceMicrophone</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Graphics</string>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Renderer Backend</string>
			<key>Key</key>
			<string>QEMURendererBackend</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Default</string>
				<string>ANGLE (OpenGL)</string>
				<string>ANGLE (Metal)</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>FPS Limit</string>
			<key>Key</key>
			<string>QEMURendererFPSLimit</string>
			<key>DefaultValue</key>
			<string>0</string>
			<key>Titles</key>
			<array>
				<string>None</string>
				<string>15</string>
				<string>30</string>
				<string>45</string>
				<string>60</string>
				<string>75</string>
				<string>90</string>
				<string>105</string>
				<string>120</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>15</integer>
				<integer>30</integer>
				<integer>45</integer>
				<integer>60</integer>
				<integer>75</integer>
				<integer>90</integer>
				<integer>105</integer>
				<integer>120</integer>
			</array>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Gestures</string>
			<key>Platform</key>
			<string>iOS</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Long Press</string>
			<key>Key</key>
			<string>GestureLongPress</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>1</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Click &amp; Hold</string>
				<string>Right Click</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Two Finger Tap</string>
			<key>Key</key>
			<string>GestureTwoTap</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>2</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Right Click</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>2</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Two Finger Pan</string>
			<key>Key</key>
			<string>GestureTwoPan</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>3</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Move Screen</string>
				<string>Click &amp; Hold</string>
				<string>Mouse Wheel</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>3</integer>
				<integer>1</integer>
				<integer>4</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Two Finger Swipe</string>
			<key>Key</key>
			<string>GestureTwoScroll</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Wheel (per swipe)</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>4</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Three Finger Pan</string>
			<key>Key</key>
			<string>GestureThreePan</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Move Screen</string>
				<string>Click &amp; Hold</string>
				<string>Mouse Wheel</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>3</integer>
				<integer>1</integer>
				<integer>4</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Cursor</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Touch Input</string>
			<key>Key</key>
			<string>MouseTouchType</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Drag cursor</string>
				<string>Touch mode (always show cursor)</string>
				<string>Touch mode (try hiding cursor)</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Visibility</string>
			<key>Key</key>
			<string>MouseTouchType</string>
			<key>Platform</key>
			<string>xrOS</string>
			<key>DefaultValue</key>
			<integer>2</integer>
			<key>Titles</key>
			<array>
				<string>Always show cursor</string>
				<string>Try hiding cursor</string>
			</array>
			<key>Values</key>
			<array>
				<integer>1</integer>
				<integer>2</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Apple Pencil Input</string>
			<key>Key</key>
			<string>MousePencilType</string>
			<key>Platform</key>
			<string>iOS</string>
			<key>DefaultValue</key>
			<integer>2</integer>
			<key>Titles</key>
			<array>
				<string>Drag cursor</string>
				<string>Tablet mode (always show cursor)</string>
				<string>Tablet mode (try hiding cursor)</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>1</integer>
				<integer>2</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Cursor - Drag Speed</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSSliderSpecifier</string>
			<key>Key</key>
			<string>DragCursorSpeed</string>
			<key>DefaultValue</key>
			<integer>100</integer>
			<key>MinimumValue</key>
			<integer>1</integer>
			<key>MaximumValue</key>
			<integer>100</integer>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Cursor - Scroll Wheel</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Invert Scroll</string>
			<key>Key</key>
			<string>InvertScroll</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Gamepad</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Menu</string>
			<key>Key</key>
			<string>GCButtonMenu</string>
			<key>DefaultValue</key>
			<integer>1</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>D-UP</string>
			<key>Key</key>
			<string>GCButtonDpadUp</string>
			<key>DefaultValue</key>
			<integer>17</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>D-LEFT</string>
			<key>Key</key>
			<string>GCButtonDpadLeft</string>
			<key>DefaultValue</key>
			<integer>30</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>D-DOWN</string>
			<key>Key</key>
			<string>GCButtonDpadDown</string>
			<key>DefaultValue</key>
			<integer>31</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>D-RIGHT</string>
			<key>Key</key>
			<string>GCButtonDpadRight</string>
			<key>DefaultValue</key>
			<integer>32</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>A</string>
			<key>Key</key>
			<string>GCButtonA</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>B</string>
			<key>Key</key>
			<string>GCButtonB</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>X</string>
			<key>Key</key>
			<string>GCButtonX</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Y</string>
			<key>Key</key>
			<string>GCButtonY</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>L1</string>
			<key>Key</key>
			<string>GCButtonShoulderLeft</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>L2</string>
			<key>Key</key>
			<string>GCButtonTriggerLeft</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>R1</string>
			<key>Key</key>
			<string>GCButtonShoulderRight</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>R2</string>
			<key>Key</key>
			<string>GCButtonTriggerRight</string>
			<key>DefaultValue</key>
			<integer>0</integer>
			<key>Titles</key>
			<array>
				<string>Disabled</string>
				<string>Mouse Left Button</string>
				<string>Mouse Right Button</string>
				<string>Mouse Middle Button</string>
				<string>Ctrl</string>
				<string>Command/Windows</string>
				<string>Option/Alt</string>
				<string>Shift</string>
				<string>Tab</string>
				<string>Space</string>
				<string>Enter</string>
				<string>Backspace</string>
				<string>Esc</string>
				<string>Caps</string>
				<string>`</string>
				<string>1</string>
				<string>2</string>
				<string>3</string>
				<string>4</string>
				<string>5</string>
				<string>6</string>
				<string>7</string>
				<string>8</string>
				<string>9</string>
				<string>0</string>
				<string>-</string>
				<string>=</string>
				<string>[</string>
				<string>]</string>
				<string>;</string>
				<string>&apos;</string>
				<string>\</string>
				<string>,</string>
				<string>.</string>
				<string>/</string>
				<string>Ins</string>
				<string>Home</string>
				<string>PgUp</string>
				<string>PgDn</string>
				<string>Del</string>
				<string>End</string>
				<string>Up</string>
				<string>Left</string>
				<string>Down</string>
				<string>Right</string>
				<string>A</string>
				<string>B</string>
				<string>C</string>
				<string>D</string>
				<string>E</string>
				<string>F</string>
				<string>G</string>
				<string>H</string>
				<string>I</string>
				<string>J</string>
				<string>K</string>
				<string>L</string>
				<string>M</string>
				<string>N</string>
				<string>O</string>
				<string>P</string>
				<string>Q</string>
				<string>R</string>
				<string>S</string>
				<string>T</string>
				<string>U</string>
				<string>V</string>
				<string>W</string>
				<string>X</string>
				<string>Y</string>
				<string>Z</string>
				<string>F1</string>
				<string>F2</string>
				<string>F3</string>
				<string>F4</string>
				<string>F5</string>
				<string>F6</string>
				<string>F7</string>
				<string>F8</string>
				<string>F9</string>
				<string>F10</string>
				<string>F11</string>
				<string>F12</string>
			</array>
			<key>Values</key>
			<array>
				<integer>0</integer>
				<integer>-1</integer>
				<integer>-3</integer>
				<integer>-2</integer>
				<integer>29</integer>
				<integer>57435</integer>
				<integer>56</integer>
				<integer>42</integer>
				<integer>15</integer>
				<integer>57</integer>
				<integer>28</integer>
				<integer>14</integer>
				<integer>1</integer>
				<integer>58</integer>
				<integer>41</integer>
				<integer>2</integer>
				<integer>3</integer>
				<integer>4</integer>
				<integer>5</integer>
				<integer>6</integer>
				<integer>7</integer>
				<integer>8</integer>
				<integer>9</integer>
				<integer>10</integer>
				<integer>11</integer>
				<integer>12</integer>
				<integer>13</integer>
				<integer>26</integer>
				<integer>27</integer>
				<integer>39</integer>
				<integer>40</integer>
				<integer>43</integer>
				<integer>51</integer>
				<integer>52</integer>
				<integer>53</integer>
				<integer>57426</integer>
				<integer>57415</integer>
				<integer>57417</integer>
				<integer>57425</integer>
				<integer>57427</integer>
				<integer>57423</integer>
				<integer>200</integer>
				<integer>203</integer>
				<integer>208</integer>
				<integer>205</integer>
				<integer>30</integer>
				<integer>48</integer>
				<integer>46</integer>
				<integer>32</integer>
				<integer>18</integer>
				<integer>33</integer>
				<integer>34</integer>
				<integer>35</integer>
				<integer>23</integer>
				<integer>36</integer>
				<integer>37</integer>
				<integer>38</integer>
				<integer>50</integer>
				<integer>49</integer>
				<integer>24</integer>
				<integer>25</integer>
				<integer>16</integer>
				<integer>19</integer>
				<integer>31</integer>
				<integer>20</integer>
				<integer>22</integer>
				<integer>47</integer>
				<integer>17</integer>
				<integer>45</integer>
				<integer>21</integer>
				<integer>44</integer>
				<integer>59</integer>
				<integer>60</integer>
				<integer>61</integer>
				<integer>62</integer>
				<integer>63</integer>
				<integer>64</integer>
				<integer>65</integer>
				<integer>66</integer>
				<integer>67</integer>
				<integer>68</integer>
				<integer>87</integer>
				<integer>88</integer>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Gamepad - Cursor Speed</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSSliderSpecifier</string>
			<key>Key</key>
			<string>GCThumbstickRightSpeed</string>
			<key>DefaultValue</key>
			<integer>10</integer>
			<key>MinimumValue</key>
			<integer>1</integer>
			<key>MaximumValue</key>
			<integer>100</integer>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>JitStreamer</string>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
				<string>iOS-SE</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Enable JitStreamer Attach</string>
			<key>Key</key>
			<string>JitStreamerAttach</string>
			<key>DefaultValue</key>
			<false/>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
				<string>iOS-SE</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTextFieldSpecifier</string>
			<key>Title</key>
			<string>JitStreamer IP Address</string>
			<key>Key</key>
			<string>JitStreamerAddress</string>
			<key>DefaultValue</key>
			<string>*********</string>
			<key>ExcludeTargets</key>
			<array>
				<string>iOS-Remote</string>
				<string>iOS-SE</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>About</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Version</string>
			<key>Key</key>
			<string>LastBootedVersion</string>
			<key>DefaultValue</key>
			<string>0.0</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Build</string>
			<key>Key</key>
			<string>LastBootedBuild</string>
			<key>DefaultValue</key>
			<string>0</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
			<key>Title</key>
			<string>License</string>
			<key>File</key>
			<string>License</string>
		</dict>
	</array>
</dict>
</plist>
